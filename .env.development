# just a flag
ENV = 'development'

# 开发环境

# base api
# // 杨郁沛
# VUE_APP_BASE_API = http://221.237.107.77:8835/api/v1/
# // 刘富程
# VUE_APP_BASE_API = http://192.168.0.57:10010/api/v1/
# // 谢梁
# VUE_APP_BASE_API = http://192.168.0.87:10010/api/v1/
# // 罗明东
# VUE_APP_BASE_API = http://192.168.0.110:10010/api/v1/
# //测试服
VUE_APP_BASE_API = http://ep.vankeytech.com:9875/api/v1/
#VUE_APP_BASE_API = 'http://manager.jinnq.com/api/v1/'

# // 李雪松
# VUE_APP_BASE_API = http://192.168.0.42:10010/api/v1/
# VUE_APP_BASE_API = http://manager.jinnq.com/api/v1/
# VUE_APP_BASE_API = http://192.168.0.54:10010/api/v1/

# http://192.168.0.30:10010/api/v1/
# http://192.168.0.135:10010/api/v1/


PILE_MODULES environment vargitiable,
# to control whether the babel-plugi
# vue-cli uses the VUE_CLI_BABEL_TRANSn-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true
