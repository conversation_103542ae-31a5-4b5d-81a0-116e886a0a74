{"name": "envi-web", "version": "0.1.0", "description": "金牛环保管理系统后台", "author": "DestinyV", "license": "MIT", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "yarn eslint && yarn stylelint", "eslint": "eslint --ext .js,.vue src --fix", "stylelint": "stylelint src/**/*.{html,vue,css,sass,scss} --fix", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop"}, "lint-staged": {"src/**/*.{js,vue}": ["vue-cli-service lint", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git"}, "bugs": {}, "dependencies": {"@liveqing/liveplayer": "^2.7.35", "@toast-ui/editor": "^3.1.3", "axios": "0.18.1", "babel-polyfill": "^6.26.0", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "^2.6.12", "crypto-js": "^4.1.1", "dayjs": "^1.10.5", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "4.2.1", "element-ui": "2.11.0", "ezuikit-js": "^0.2.3", "file-saver": "^2.0.1", "flv.js": "^1.5.0", "fuse.js": "3.4.4", "gojs": "^2.0.17", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "less": "^4.1.1", "lodash": "^4.17.15", "moment": "^2.25.3", "mxreality.js": "^1.2.25", "normalize.css": "7.0.0", "nprogress": "0.2.0", "overlayscrollbars": "^1.13.1", "overlayscrollbars-vue": "^0.2.2", "path-to-regexp": "2.4.0", "sass": "^1.52.3", "screenfull": "4.2.0", "showdown": "1.9.0", "sockjs-client": "^1.4.0", "sortablejs": "1.8.4", "stompjs": "^2.3.3", "swiper": "^5.4.5", "v-charts": "^1.19.0", "v-distpicker": "^1.0.20", "vue": "2.6.10", "vue-awesome-swiper": "^4.1.1", "vue-count-to": "1.0.13", "vue-pdf": "^4.3.0", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "xlsx": "^0.14.1", "xlsx-style": "^0.8.13", "yarn": "^1.22.21"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/eslint-config-airbnb": "^5.3.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^8.2.2", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-jest": "23.6.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.7.0", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "cropper": "^4.1.0", "eslint": "^5.15.3", "eslint-config-airbnb": "^18.2.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "husky": "^4.2.5", "less-loader": "^7.3.0", "lint-staged": "^10.2.11", "mockjs": "1.0.1-beta3", "node-sass": "^8.0.0", "plop": "2.3.0", "runjs": "^4.3.2", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "stylelint": "^13.13.1", "stylelint-config-recess-order": "^2.4.0", "stylelint-config-standard": "^22.0.0", "stylelint-prettier": "^1.2.0", "stylelint-scss": "^3.19.0", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-cropper": "^0.4.9", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}