<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta
    name="description"
    content="环保环卫数字化综合管控平台"
  >
  <meta
    name="keywords"
    content="万开科技 vankey 环保 科技"
  >
  <meta
    http-equiv="X-UA-Compatible"
    content="IE=edge,chrome=1"
  >
  <meta
    name="renderer"
    content="webkit"
  >
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
  >
  <link
    rel="shortcut icon"
    href="./favicon.ico"
  >
  <title><%= webpackConfig.name %></title>
  <script
    type="text/javascript"
    src="https://api.map.baidu.com/api?v=3.0&ak=rfYC84g7OSwfLG6bNnSpQemRhXuWuDFy&s=1"
  >
  </script>
  <script
    src="https://webapi.amap.com/maps?v=1.4.15&key=aebe6f286a8f79cc86e5a819c7003bb8&plugin=AMap.DistrictSearch&plugin=AMap.Heatmap&AMap.ControlBar&plugin=AMap.Object3DLayer&plugin=Map3D&plugin=AMap.Geocoder&plugin=AMap.MouseTool&plugin=AMap.PlaceSearch&plugin=AMap.CanvasLayer"
  >
  </script>
  <script src="https://webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>
  <script src="./liveplayer-lib.min.js"></script>
  <!--加载鼠标绘制工具-->
  <script
    type="text/javascript"
    src="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.js"
  ></script>
  <!--加载检索信息窗口-->
  <script
    type="text/javascript"
    src="https://api.map.baidu.com/library/SearchInfoWindow/1.4/src/SearchInfoWindow_min.js"
  ></script>
  <link
    rel="stylesheet"
    href="https://api.map.baidu.com/library/DrawingManager/1.4/src/DrawingManager_min.css"
  />
  <link
    rel="stylesheet"
    href="https://api.map.baidu.com/library/SearchInfoWindow/1.4/src/SearchInfoWindow_min.css"
  />
  <script
    type="text/javascript"
    src="https://api.map.baidu.com/library/AreaRestriction/1.2/src/AreaRestriction_min.js"
  ></script>
  <link
    rel="stylesheet"
    href="https://g.alicdn.com/de/prismplayer/2.9.1/skins/default/aliplayer-min.css"
  />
  <script
    type="text/javascript"
    charset="utf-8"
    src="https://g.alicdn.com/de/prismplayer/2.9.1/aliplayer-min.js"
  ></script>
  <script src="jessibuca/index.js"></script>

  <script type="text/javascript">
    window.addEventListener('mousedown', onMouseDown, false);
    //关闭网页的右键事件
    document.oncontextmenu = function (event) {
      if (event.srcElement._prevClass && event.srcElement._prevClass.indexOf('el-image__inner') ==
        '-1') {
        return false
      }
    }

    function onMouseDown(event) {
      if (event.button === 0) {
        console.log("鼠标左键!")
      } else if (event.button === 2) {
        console.log("鼠标右键!");
      }
    }

  </script>
</head>

<body>
  <div id="app"></div>

  <!-- built files will be auto injected -->
</body>

</html>
