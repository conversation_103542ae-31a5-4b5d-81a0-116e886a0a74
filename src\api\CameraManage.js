import request from '@/utils/request'
/* 分页 */
export function logincamera(data) {
  return request({
    url: '/waterSource/camera/page',
    method: 'post',
    data
  })
}

/* 新增摄像机*/
export function savecamera(data) {
  return request({
    url: '/waterSource/camera/save',
    method: 'post',
    data
  })
}

/* 编辑摄像机*/
export function cameraupdate(data) {
  return request({
    url: '/waterSource/camera/update',
    method: 'put',
    data
  })
}

/* 拉取摄像头快照*/
export function snapshot(data) {
  return request({
    url: `/waterSource/camera/snapshot?cameraId=${data}`,
    method: 'put'
  })
}

/* 删除摄像机*/
export function deletecamera(id) {
  return request({
    url: `/waterSource/camera/delete?ids=${id}`,
    method: 'DELETE'
  })
}

/* 选择配置列表*/
export function templatelist() {
  return request({
    url: `/waterSource/behavior-analysis-setting/list`,
    method: 'get'
  })
}

/* 选择配置列表*/
export function itemlist() {
  return request({
    url: `/waterSource/project/list`,
    method: 'get'
  })
}
