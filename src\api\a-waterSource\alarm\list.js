import request from '@/utils/request'

/**
 * 获取告警列表
 * @param {Object} data {
    pageNum,
    pageSize,
    subAlarmType, 告警子类型:1水质、2水量、3巡岗、4行为、5设备
    stationId, 站点id================》必传
    begin,开始时间 yyyy-MM-dd HH:mm:ss
    end 结束时间 yyyy-MM-dd HH:mm:ss
  }
 * @returns promise
 */
export function getAlarmList(data) {
  const {
    pageNum, pageSize, subAlarmType, stationId, begin, end, keyWord, deviceType
  } = data

  // if (!stationId) return Promise.reject(new Error('请传入监测站Id'))

  return request({
    url: '/water/water-source-alarm/page',
    method: 'post',
    data: {
      pageNum: pageNum || 1,
      pageSize: pageSize || 10,
      query: {
        stationId,
        subAlarmType,
        keyWord,
        deviceType,
        begin,
        end
      }
    }
  })
}

export function getListKeyWords(data) {
  return request({
    url: '/water/water-source-alarm/listKeyWords',
    method: 'get'
  })
}

export function getListDeviceType(data) {
  return request({
    url: '/water/water-source-alarm/listDeviceType',
    method: 'get'
  })
}

export default {}
