import request from '@/utils/request'

/**
 * 获取告警阈值设置列表
 * @param {Object} data {
    pageNum,
    pageSize,
    stationName, 站点名称
    stationId 站点id
  }
 * @returns promise
 */
export function getAlarmSetList(data) {
  const {
    pageNum, pageSize, stationName, stationId
  } = data

  // if (!stationId) return Promise.reject(new Error('请传入监测站Id'))

  return request({
    url: '/water/station/alarm/setting/page',
    method: 'post',
    data: {
      pageNum: pageNum || 1,
      pageSize: pageSize || 10,
      query: {
        stationId,
        stationName
      }
    }
  })
}

/**
 * 一键设置所有阈值
 * @param {Object} data {
    "ph": {
        "alarmValue": "告警值",
        "alarmMinValue": "最小值",
        "alarmMaxValue": "最大值",
        "valueType": "0 大于固定值报警, 1 范围内正常 2 范围外正常"
    },
    "nh3Nh4": "",
    "p": "",
    "sDo": "",
    "mno4": ""
  }
 * @returns promise
 */
export function totalSeting(data) {
  return request({
    url: '/water/station/alarm/total/setting',
    method: 'PUT',
    data
  })
}

export default {}
