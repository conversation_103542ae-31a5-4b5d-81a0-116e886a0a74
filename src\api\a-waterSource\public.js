import request from '@/utils/request'
// ----------公共接口------------

/**
 * @description 上传文件
 * @needData data-文件
 * <AUTHOR>
export function uploadFile(data, callback1) {
  return request({
    url: `/task/file/upload`,
    method: 'post',
    timeout: 600000,
    errorMsg: '文件上传失败，请减小文件体积或更换更良好的网络环境',
    onUploadProgress: (progressEvent) => { // 原生获取上传进度的事件
      if (progressEvent.lengthComputable) {
        // 属性lengthComputable主要表明总共需要完成的工作量和已经完成的工作是否可以被测量
        // 如果lengthComputable为false，就获取不到progressEvent.total和progressEvent.loaded
        // eslint-disable-next-line no-mixed-operators
        const percent = progressEvent.loaded / progressEvent.total * 100
        if (callback1) {
          callback1(percent)
        }
      }
    },
    data
  })
}
export default {}
