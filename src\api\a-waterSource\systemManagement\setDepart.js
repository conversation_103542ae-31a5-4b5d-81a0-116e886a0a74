import request from '@/utils/request'
// ----------部门管理接口------------

/**
 * @description 部门菜单树
 * @needData 无
 * <AUTHOR>
 */
export function departmentListAll() {
  return request({
    url: '/system/department/list',
    method: 'get'
  })
}
/**
 * @description 根据上级部门id查询部门集合
 * @needData accountId-上级部门id
 * <AUTHOR>
 */
export function departmentIdList(accountId) {
  return request({
    url: '/system/department/getDepartmentAccount/',
    method: 'get',
    params: { accountId }
  })
}
/**
 * @description 删除部门
 * @needData departmentId-部门id
 * <AUTHOR>
 */
export function delDepartment(departmentId) {
  return request({
    url: '/system/department/deleteDepartmentAccount',
    method: 'get',
    params: { departmentId }
  })
}

/**
 * @description 权限菜单树
 * @needData type-菜单树类型（1 管理端 2 小程序 3 大数据）
 * <AUTHOR>
 */
export function menuTree(type) {
  return request({
    url: `/system/department/menu/tree?type=${type}`,
    method: 'get'
  })
}
// /**
//  * @description 某个账号的权限信息
//  * @needData accountId-账号id
//  * <AUTHOR>
//  */
// export function getDepartmentAccount(accountId) {
//   return request({
//     url: `/department/getDepartmentAccount?accountId=${accountId}`,
//     method: 'get'
//   })
// }
/**
 * @description 数据权限列表(部门名称列表)
 * @needData 无
 * <AUTHOR>
 */
export function dataPermissionSelection() {
  return request({
    url: `/system/department/dataPermissionSelection`,
    method: 'get'
  })
}
/**
 * @description 修改部门3.0
 * @needData data-修改后的数据
 * <AUTHOR>
 */
export function doUpdate(data) {
  return request({
    url: `/system/department/doUpdate`,
    method: 'put',
    data
  })
}
/**
 * @description 新增部门3.0
 * @needData data-新增的数据
 * <AUTHOR>
 */
export function doSave(data) {
  return request({
    url: `/system/department/doSave`,
    method: 'post',
    data
  })
}
/**
 * @description 获取当前部门拥有的部门权限
 * @needData departmentId-部门id
 * <AUTHOR>
 */
export function getDepartmentDataList(departmentId) {
  return request({
    url: `/system/department/getDepartmentDataList?departmentId=${departmentId}`,
    method: 'get'
  })
}
