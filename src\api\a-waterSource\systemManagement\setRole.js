import request from '@/utils/request'
// ----------人员管理接口------------

/**
 * @description 部门菜单树
 * @needData 无
 * <AUTHOR>
 */
export function departmentListAll() {
  return request({
    url: '/system/department/list',
    method: 'get'
  })
}
/**
 * @description 删除部门
 * @needData departmentId-部门id
 * <AUTHOR>
 */
export function delDepartment(departmentId) {
  return request({
    url: '/system/department/deleteDepartmentAccount',
    method: 'get',
    params: { departmentId }
  })
}

/**
 * @description 重置密码为 123456
 * @needData accountId-账号id
 * <AUTHOR>
 */
export function restartPassword({ accountId }) {
  return request({
    url: `/system/user/restartPassword?accountId=${accountId}`,
    method: 'put'
  })
}
/**
 * @description 判断账号是否重复
 * @needData accountId-账号id
 * <AUTHOR>
 */
export function existAccoun(accountId) {
  return request({
    url: `/system/user/exist?account=${accountId}`,
    method: 'get'
  })
}
/**
 * @description 新增人员3.0
 * @needData data-新增的人员数据
 * <AUTHOR>
 */
export function doSave(data) {
  return request({
    url: `/system/user`,
    method: 'post',
    data
  })
}
/**
 * @description 更新人员3.0
 * @needData data-修改后的数据
 * <AUTHOR>
 */
export function doUpdate(data) {
  return request({
    url: `/system/user`,
    method: 'put',
    data
  })
}
/**
 * @description 根据部门id 查询部门管理的 菜单权限
 * @needData departmentId-部门id
 * <AUTHOR>
 */
export function departmentMenu(departmentId) {
  return request({
    url: `/system/systemMenu/getAllMenu?departmentId=${departmentId}`,
    method: 'get'
  })
}
/**
 * @description 获取当前部门拥有的部门权限
 * @needData departmentId-部门id
 * <AUTHOR>
 */
export function getDepartmentDataList(departmentId) {
  return request({
    url: `/system/department/getDepartmentDataList?departmentId=${departmentId}`,
    method: 'get'
  })
}
