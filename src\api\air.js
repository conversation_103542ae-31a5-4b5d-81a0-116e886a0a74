import request from '@/utils/request'

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气质量考核
 */
export function airCheck() {
  return request({
    url: '/air/air_station/aqi_quality/check',
    method: 'get'
  })
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气质量考核修改
 */
export function airQuilaty(data) {
  return request({
    url: '/air/air_monitor/quality_check',
    method: 'put',
    data
  })
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 自动刷新空气考核
 */
export function autoRefresh() {
  return request({
    url: '/air/quality/auto_refresh',
    method: 'get'
  })
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气 空气质量综合指数
 */
export function aqci() {
  return request({
    url: '/air/air-county-month-aqci/aqci',
    method: 'get'
  })
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 区 12个月环比
 */
export function ringAqci() {
  return request({
    url: '/air/air-county-month-aqci/ring/aqci',
    method: 'get'
  })
}

export function getFutureAir() {
  return request({
    url: '/air/air_station/future_air/aqiPredict',
    method: 'get'
  })
}

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气质量趋势
 */
export function aqiQualityTrend(data) {
  return request({
    url: `/air/air_station/aqi_quality/trend?pollutantCode=${data.pollutantCode}`,
    method: 'get'
  })
}

export function airQualityRanking(data) {
  return request({
    url: '/air/air_monitor/air_quality_ranking',
    method: 'get',
    params: data
  })
}

export function getWeather() {
  return request({
    url: '/air/weather/recent',
    method: 'get'
  })
}

export function getAirCounty(data) {
  return request({
    url: '/air/county/hour/recent',
    method: 'get',
    params: data
  })
}

export function getStationList() {
  return request({
    url: '/air/air_monitor/station_option_list',
    method: 'get'
  })
}

export function getDataCheckList(data) {
  return request({
    url: '/air/air_monitor/data_check_list',
    method: 'post',
    data,
    timeout: 10000
  })
}

export function exportDataCheck(data) {
  return request({
    url: '/air/air_monitor/data_check/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: data
  })
}
/**
 * 获取待办任务数量
 * @param Object args
 * @param {*} userId 登录用户id
 */
export function getTaskCount(userId) {
  return request({
    url: '/task/getMyTodoCount',
    method: 'get',
    params: {
      userId
    }
  })
}

