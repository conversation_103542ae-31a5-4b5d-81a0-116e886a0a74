// 空气质量排行报表api

// 获取日报
import request from '@/utils/request'

// 获取日报table
export function getDayData(data) {
  return request({
    url: '/air/street/report/day',
    method: 'post',
    data
  })
}

// 获取周报数据
export function getWeekData(data) {
  return request({
    url: '/air/street/report/week',
    method: 'post',
    data
  })
}

// 导出数据
export function exportExcel(data, url) {
  return request({
    url: `/air/street/report${url}`,
    method: 'post',
    params: data,
    responseType: 'blob'
  })
}

// 获取月报数据
export function getMonthData(data) {
  return request({
    url: '/air/street/report/month',
    method: 'post',
    data
  })
}

// 获取季度数据
export function getQuarterData(data) {
  return request({
    url: '/air/street/report/season',
    method: 'post',
    data
  })
}
// 获取年度数据
export function getYearData(data) {
  return request({
    url: '/air/street/report/year',
    method: 'post',
    data
  })
}

