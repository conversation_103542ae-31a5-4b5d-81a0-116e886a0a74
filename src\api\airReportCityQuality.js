// 城市空气质量报表api

// 获取日报
import request from '@/utils/request'

// 获取日报table
export function getDayData(data) {
  return request({
    url: '/air/report/day',
    method: 'post',
    data
  })
}

// 获取周报数据
export function getWeekData(data) {
  return request({
    url: '/air/report/week',
    method: 'post',
    data
  })
}

// 导出数据
export function exportExcel(data, url) {
  return request({
    url: `/air/report${url}`,
    method: 'post',
    timeout: 300000,
    data,
    'Content-Type': 'multipart/form-data',
    responseType: 'blob'
  })
}

// 获取月报数据
export function getMonthData(data) {
  return request({
    url: '/air/report/month',
    method: 'post',
    data
  })
}

// 获取季度数据
export function getQuarterData(data) {
  return request({
    url: '/air/report/season',
    method: 'post',
    data
  })
}
// 获取年度数据
export function getYearData(data) {
  return request({
    url: '/air/report/year',
    method: 'post',
    data
  })
}

