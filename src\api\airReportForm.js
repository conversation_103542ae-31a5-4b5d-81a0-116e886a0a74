import request from '@/utils/request'

export function getStationList(areaCode) {
  let url = ''
  if (areaCode) {
    url = `/air/air_station/typeStation?areaCode=${areaCode}`
  } else {
    url = `/air/air_station/typeStation`
  }
  return request({
    url,
    method: 'get'
  })
}

export function airPollutantReportDay(data) {
  return request({
    url: '/air/pollutant/report/day',
    method: 'post',
    data
  })
}

export function airPollutantReportMonth(data) {
  return request({
    url: '/air/pollutant/report/month',
    method: 'post',
    data
  })
}

export function airPollutantReportDayExport(data) {
  return request({
    url: '/air/pollutant/report/day/export',
    method: 'post',
    'Content-Type': 'multipart/form-data',
    responseType: 'blob',
    data
  })
}
// 导出月度浓度报表
export function airPollutantReportMonthExport(data) {
  return request({
    url: '/air/pollutant/report/month/export',
    method: 'post',
    'Content-Type': 'multipart/form-data',
    responseType: 'blob',
    data
  })
}
// 根据站点获取污染物列表
export function getpollutionCodes(stationCode) {
  return request({
    url: `/air/air_station/pollution_codes?stationCode=${stationCode}`,
    method: 'get'
  })
}
// 获取空气历史分钟数据
export function getlistMinuteItem(data) {
  return request({
    url: `/air/minute/listItemByStation`,
    method: 'post',
    data
  })
}
// 获取空气历史小时数据
export function getlistHourItem(data) {
  return request({
    url: `/air/air-station-hour-aqi/listItemByStation`,
    method: 'post',
    data
  })
}

// 获取区域
export function getDistrict(params) {
  return request({
    url: `/air/area/district`,
    method: 'get',
    params
  })
}

