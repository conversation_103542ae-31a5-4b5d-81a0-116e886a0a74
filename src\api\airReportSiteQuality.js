// 站点空气质量报表api

// 获取日报
import request from '@/utils/request'
// 获取分钟报数据
export function getMinuteData(data) {
  return request({
    url: '/air/station/report/minute',
    method: 'post',
    data,
    timeout: 10000
  })
}

// 获取实时报数据
export function getHourData(data) {
  return request({
    url: '/air/station/report/hour',
    method: 'post',
    data,
    timeout: 10000
  })
}
// 获取日报table
export function getDayData(data) {
  return request({
    url: '/air/station/report/day',
    method: 'post',
    data
  })
}
// 获取周报数据
export function getWeekData(data) {
  return request({
    url: '/air/station/report/week',
    method: 'post',
    data
  })
}

// 导出数据
export function exportExcel(data, url) {
  return request({
    url: `/air/station/report${url}`,
    method: 'post',
    'Content-Type': 'multipart/form-data',
    data,
    responseType: 'blob',
    timeout: 10000
  })
}

// 获取月报数据
export function getMonthData(data) {
  return request({
    url: '/air/station/report/month',
    method: 'post',
    data
  })
}

// 获取季度数据
export function getQuarterData(data) {
  return request({
    url: '/air/station/report/season',
    method: 'post',
    data
  })
}
// 获取年度数据
export function getYearData(data) {
  return request({
    url: '/air/station/report/year',
    method: 'post',
    data
  })
}

