import request from '@/utils/request'
export function airPollutionDiffusionPageList(params) {
    return request({
      url: '/air/air_pollution_diffusion/doPage',
      method: 'get',
      params
    })
  }
  
  export function airPollutionDiffusionExport(params) {
    return request({
      url: '/air/air_pollution_diffusion/export',
      'Content-Type': 'multipart/form-data',
      responseType: 'blob',
      params
    })
  }