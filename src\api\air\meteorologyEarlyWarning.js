import request from '@/utils/request'

export function meteorologyEarlyWarningPageList(params) {
  return request({
    url: '/air/meteorology_early_warning/pageList',
    method: 'get',
    params
  })
}

export function meteorologyEarlyWarningExport(params) {
  return request({
    url: '/air/meteorology_early_warning/export',
    'Content-Type': 'multipart/form-data',
    responseType: 'blob',
    params
  })
}
// 获取报警类型
export function getAlarmTypeList() {
  return request({
    url: '/air/meteorology_early_warning/listType',
    method:'get',
  })
}