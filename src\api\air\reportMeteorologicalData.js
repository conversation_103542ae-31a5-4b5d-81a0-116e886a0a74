import request from '@/utils/request'

export function weatherStationList(params) {
  let url = ''
  if (params) {
    url = `/air/weather_station/list?district=${params}`
  } else {
    url = '/air/weather_station/list'
  }
  return request({
    url,
    method: 'get'
  })
}

export function weatherHourPageList(params) {
  return request({
    url: '/air/weather/hour/pageList',
    method: 'get',
    params
  })
}

export function weatherDayPageList(params) {
  return request({
    url: '/air/weather/day/pageList',
    method: 'get',
    params
  })
}

export function weatherHourExport(params) {
  return request({
    url: '/air/weather/hour/export',
    'Content-Type': 'multipart/form-data',
    responseType: 'blob',
    params
  })
}

export function weatherDayExport(params) {
  return request({
    url: '/air/weather/day/export',
    'Content-Type': 'multipart/form-data',
    responseType: 'blob',
    params
  })
}
