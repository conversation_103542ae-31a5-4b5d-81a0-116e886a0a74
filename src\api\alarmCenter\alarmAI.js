import request from "@/utils/request";

// 分页获取视频分析告警列表
export function pageList(params) {
  return request({
    url: "/water/analysis/alarm/pageList",
    method: "get",
    params
  });
}

// 获取告警类型列表
export function getAlarmTypeList() {
  return request({
    url: "/water/analysis/alarm/alarmType",
    method: "get"
  });
}

// 导出视频分析告警列表
export function exportAlarm(params) {
  return request({
    url: "/water/analysis/alarm/exportAlarm",
    method: "get",
    params,
    timeout: 100000,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    responseType: "blob"
  });
}
