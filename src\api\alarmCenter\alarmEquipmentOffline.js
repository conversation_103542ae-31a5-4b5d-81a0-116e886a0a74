import request from '@/utils/request'

// 获取站点类型列表
// eslint-disable-next-line import/prefer-default-export
export function listType() {
  return request({
    url: '/air/all-station-offline-alarm/listType',
    method: 'get'
  })
}
// 分页获取告警列表
export function pageList(params) {
  return request({
    url: '/air/all-station-offline-alarm/pageList',
    method: 'get',
    params
  })
}
// 导出告警列表
export function exportAlarm(params) {
  return request({
    url: '/air/all-station-offline-alarm/exportAlarm',
    method: 'get',
    params,
    timeout: 100000,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob'
  })
}
