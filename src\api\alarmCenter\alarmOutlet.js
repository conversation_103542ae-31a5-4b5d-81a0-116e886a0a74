import request from '@/utils/request'

// 获取水质告警项列表
// eslint-disable-next-line import/prefer-default-export
export function exportAlarm(params) {
  return request({
    url: '/water/water-drain-alarm/exportAlarm',
    method: 'get',
    params,
    timeout: 100000,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob'
  })
}
// 获取类型
export function pageList(params) {
  return request({
    url: '/water/water-drain-alarm/pageList',
    method: 'get',
    params
  })
}

