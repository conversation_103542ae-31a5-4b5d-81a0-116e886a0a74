import request from '@/utils/request'

// 获取告警项列表
// eslint-disable-next-line import/prefer-default-export
export function listAlarmItem(params) {
  return request({
    url: '/water/sewage/alarm/listItem',
    method: 'get',
    params
  })
}
// 分页获取告警列表
export function pageList(params) {
  return request({
    url: '/water/sewage/alarm/pageList',
    method: 'get',
    params
  })
}
// 获取污染源站点列表
export function listSewageStation(params) {
  return request({
    url: '/water/sewage/alarm/listSewageStation',
    method: 'get',
    params
  })
}
// 获取类型
export function listType() {
  return request({
    url: '/water/sewage/alarm/listType',
    method: 'get'
  })
}

// 导出告警列表
export function exportAlarm(params) {
  return request({
    url: '/water/sewage/alarm/exportAlarm',
    method: 'get',
    params,
    timeout: 100000,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob'
  })
}
