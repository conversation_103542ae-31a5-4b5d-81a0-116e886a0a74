import request from '@/utils/request'

// 获取水质告警项列表
// eslint-disable-next-line import/prefer-default-export
export function listAlarmItem() {
  return request({
    url: '/water/alarm/listAlarmItem',
    method: 'get'
  })
}
// 分页获取告警列表
export function pageList(params) {
  return request({
    url: '/water/alarm/pageList',
    method: 'get',
    params
  })
}
// 导出空气告警列表
export function exportAlarm(params) {
  return request({
    url: '/water/alarm/exportAlarm',
    method: 'get',
    params,
    timeout: 100000,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob'
  })
}
