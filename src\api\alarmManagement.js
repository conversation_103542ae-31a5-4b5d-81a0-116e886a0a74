import request from '@/utils/request'
// 获取告警数据table
export function getAlarmTableList(data) {
  return request({
    url: '/air/air-alarm/all/pageList',
    method: 'post',
    data
  })
}
// 获取告警数据详情
export function getAlarmDetail(data) {
  return request({
    url: '/joint_law_enforcement_task/listAlarmByEnforcementTaskId',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params: data
  })
}

// 获取电子围栏信息
export function getVehicleList(
  pageNum,
  pageSize,
  deptId,
  startDate,
  endDate
) {
  return request({
    url: '/car/cm_alarm/getCarAlarmPage',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      deptId,
      startDate,
      endDate
    }
  })
}

// 获取区域列表
export function getAreaList() {
  return request({
    url: '/car/cm_alarm/getDeptList',
    method: 'get'
  })
}

// 查看监控列表
export function getMonitorList(taskId) {
  return request({
    url: '/task/getMonitorByTaskId',
    method: 'get',
    params: { taskId }
  })
}
