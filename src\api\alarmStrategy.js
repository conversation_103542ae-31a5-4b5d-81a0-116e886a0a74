import request from '@/utils/request'
// 新增告警策略
export function save(data) {
  return request({
    url: '/air/air-station-alarm-setting/save',
    method: 'post',
    data
  })
}
// 编辑告警策略
export function update(data) {
  return request({
    url: '/air/air-station-alarm-setting/update',
    method: 'put',
    data
  })
}
// 编辑告警策略
export function settingDelete(data) {
  return request({
    url: '/air/air-station-alarm-setting/delete',
    method: 'delete',
    data
  })
}
// 分页查询告警策略列表
export function pageList(data) {
  return request({
    url: '/air/air-station-alarm-setting/pageList',
    method: 'post',
    data
  })
}
