import request from '@/utils/request'

export function getWaterCameraList(data) {
  return request({
    url: '/water/water-camera/listByStation',
    method: 'post',
    data
  })
}

export function getLive(cameraSerial) {
  return request({
    url: `/car/camera/live/${cameraSerial}`,
    method: 'get'
  })
}
export function getCarCameraList(params) {
  return request({
    url: '/car/camera/listByKeyword',
    method: 'get',
    params
  })
}
// 获取水监控信息
export function getWaterLive(id) {
  return request({
    url: `/water/water-camera/live/${id}`,
    method: 'get'
  })
}

// 获取汽修监控播放地址
export function getCarLive(deviceSerial) {
  return request({
    url: `/water/garage-camera/getLiveAddress/${deviceSerial}`,
    method: 'get'
  })
}

// 全景监控列表
export function getPanoramicList() {
  return request({
    url: '/water/panoramic/camera/list',
    method: 'get'
  })
}

// 云台控制
export function ptzControl(data) {
  return request({
    url: '/water/garage-camera/ptz',
    method: 'post',
    data
  })
}
