import request from '@/utils/request'

/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取监控列表
 */
// eslint-disable-next-line import/prefer-default-export
export function pageList(params) {
  return request({
    url: '/air/web/lidar-camera/pageList',
    method: 'get',
    params
  })
}
export function getCameraLive(channelId) {
  return request({
    url: `/air/web/lidar-camera/getLiveAddress/${channelId}`,
    method: 'get',
    timeout: 100000
  })
}
