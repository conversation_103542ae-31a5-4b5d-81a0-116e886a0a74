/* eslint-disable */
import request from '@/utils/request'


/**  全景监控 start  */
// 获取全景监控视频列表
export function panoramicCameraList(data) {
  return request({
     url: '/water/panoramic/camera/list',
    method: 'get',
    data
  })
}
// 获取全景监控详情
export function panoramicCameraLive(channelId) {
  return request({
    url: `/water/panoramic/camera/live/${channelId}`,
    method: 'get',
    timeout: 60 * 1000
  })
}
