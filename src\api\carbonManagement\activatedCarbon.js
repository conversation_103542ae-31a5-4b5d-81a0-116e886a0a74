import request from '@/utils/request'

export function activatedCarbonPageList(params) {
  return request({
    url: '/system/activated_carbon/pageList',
    method: 'get',
    params
  })
}

export function activatedCarbonPageListByType(params) {
  return request({
    url: '/system/activated_carbon/pageListByType',
    method: 'get',
    params
  })
}
export function activatedCarbonUpdate(data) {
  return request({
    url: '/system/activated_carbon/update',
    method: 'put',
    data
  })
}

export function activatedCarbonTypeList(params) {
  return request({
    url: '/system/activated_carbon_type/list',
    method: 'get',
    params
  })
}
// 区县下拉
export function getDistrictList() {
  return request({
    url: '/system/pollutionSourceSurvey/doDistrictList',
    method: 'get'
  })
}
// 污染源监管下拉
export function getPollutionTypeList() {
  return request({
    url: '/system/supervision_type/list',
    method: 'get'
  })
}
// 导出企业活性炭信息
export function exportActivatedCarbon(params) {
  return request({
    url: '/system/activated_carbon/export',
    method:'get',
    responseType: 'blob',
    params
  })
}
// 街道下拉
export function getStreetList(code) {
  return request({
    url: `/system/pollutionSourceSurvey/doStreetList?code=${code}`,
    method: 'get',
  })
}