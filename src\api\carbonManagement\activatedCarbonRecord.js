import request from '@/utils/request'

export function activatedCarbonRecordPageList(params) {
  return request({
    url: '/system/activated_carbon_record/pageList',
    method: 'get',
    params
  })
}

export function activatedCarbonRecordGetDetail(params) {
  return request({
    url: '/system/activated_carbon_record/getDetail',
    method: 'get',
    params
  })
}

// 区县下拉
export function getDistrictList() {
  return request({
    url: '/system/pollutionSourceSurvey/doDistrictList',
    method: 'get'
  })
}
// 污染源监管下拉
export function getPollutionTypeList() {
  return request({
    url: '/system/supervision_type/list',
    method: 'get'
  })
}
// 导出活性炭上报信息
export function exportActivatedCarbonRecord(params) {
  return request({
    url: '/system/activated_carbon_record/export',
    method:'get',
    responseType: 'blob',
    params
  })
}
// 导出活性炭更换周期
export function exportActivatedCarbonType(params) {
  return request({
    url: '/system/activated_carbon/exportByType',
    method:'get',
    responseType: 'blob',
    params
  })
}
// 街道下拉
export function getStreetList(code) {
  return request({
    url: `/system/pollutionSourceSurvey/doStreetList?code=${code}`,
    method: 'get',
  })
}