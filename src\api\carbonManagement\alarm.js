import request from '@/utils/request'

export function alarmPageList(params) {
  return request({
    url: '/system/web/activated_carbon_alarm/pageList',
    method: 'get',
    params
  })
}

export function alarmDetail(params) {
  return request({
    url: '/system/web/activated_carbon_alarm/getDetail',
    method: 'get',
    params
  })
}

// 忽略告警
export function alarmIgnore(data) {
  return request({
    url: '/system/web/activated_carbon_alarm/ignore',
    method: 'put',
    data
  })
}

// 活性炭告警导出
export function alarmExport(params) {
  return request({
    url: '/system/web/activated_carbon_alarm/export',
    'Content-Type': 'multipart/form-data',
    responseType: 'blob',
    params
  })
}

// 生成任务
export function generateTask(data) {
  return request({
    url: `/system/web/activated_carbon_alarm/toTask`,
    method: 'post',
    timeout: 300000,
    data
  })
}

