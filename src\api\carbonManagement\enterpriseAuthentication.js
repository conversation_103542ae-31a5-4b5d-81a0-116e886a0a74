import request from '@/utils/request'

export function enterprisePageList(params) {
  return request({
    url: '/system/enterprise/pageList',
    method: 'get',
    params
  })
}

export function enterpriseVerify(data) {
  return request({
    url: '/system/enterprise/verify',
    method: 'put',
    data
  })
}
export function enterpriseAdd(data) {
  return request({
    url: '/system/enterprise/add',
    method: 'post',
    data
  })
}

export function getCountType() {
  return request({
    url: '/system/enterprise/typeCount',
    method: 'get'
  })
}
// 导出企业信息
export function exportEnterprise(params) {
  return request({
    url: '/system/enterprise/export',
    method:'get',
    responseType: 'blob',
    params
  })
}