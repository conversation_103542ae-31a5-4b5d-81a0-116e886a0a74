import request from '@/utils/request'

/** 分页 */
export function getList(data) {
  return request({
    url: '/waterSource/client/page',
    method: 'post',
    data
  })
}
/** 新增 */
export function saveClient(data) {
  return request({
    url: '/waterSource/client/save',
    method: 'post',
    data
  })
}
/** 编辑 */
export function updateClient(data) {
  return request({
    url: '/waterSource/client/update',
    method: 'put',
    data
  })
}
/** 删除 */
export function deleteClient({ id }) {
  return request({
    url: `/waterSource/client/delete?ids=${id}`,
    method: 'delete'
  })
}

/** 重启 */
export function restartClient(params) {
  return request({
    url: '/waterSource/client/restart',
    method: 'put',
    params
  })
}
/** 开启/关闭 frp */
export function openOrCloseFrp(params) {
  return request({
    url: '/waterSource/client/openOrCloseFrp',
    method: 'put',
    params
  })
}
/** 拉取日志 */
export function pullLogClient(params) {
  return request({
    url: '/waterSource/client/log',
    method: 'put',
    params
  })
}
/** 拉取日志 */
export function upgradeClient(params) {
  return request({
    url: '/waterSource/client/upgrade',
    method: 'put',
    params
  })
}
/* 项目列表*/
export function itemlist() {
  return request({
    url: `/project/list`,
    method: 'get'
  })
}

