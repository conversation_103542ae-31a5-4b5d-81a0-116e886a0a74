import request from '@/utils/request'
// 获取应急指挥列表
export function getTaskListData(data) {
  return request({
    url: '/task/getTaskPage',
    method: 'get',
    params: data
  })
}
export function getDepartmentList(departmentId) {
  return request({
    url: '/task/emergency_command_task/department',
    method: 'get',
    params: {
      departmentId
    }
  })
}

// 获取应急指挥详情
export function getCommentDetails(taskId, userId) {
  return request({
    url: '/task/getTaskDetail',
    method: 'get',
    params: {
      taskId,
      userId
    }
  })
}

// 新增应急任务
export function addTask(data) {
  return request({
    url: '/task/createTask',
    method: 'post',
    data
  })
}

// 获取应急指挥流程图详情
export function getFloatData(id) {
  return request({
    url: `/task/emergency_command_task/taskProcessFlow/${id}`,
    method: 'get'
  })
}

// 应急指挥任务指派
export function assignmentTask(data) {
  return request({
    url: '/task/emergency_command_task/create-child',
    method: 'post',
    data
  })
}

// 完成任务
export function confirmComplted(data) {
  return request({
    url: '/task/emergency_command_task/complete',
    method: 'post',
    data
  })
}

// 任务反馈
export function confirmComment(data) {
  return request({
    url: '/task/emergency_command_task/remark',
    method: 'post',
    data
  })
}

// 日志列表
export function getTaskLogData(tid) {
  return request({
    url: '/task/emergency_command_task/log',
    method: 'get',
    params: {
      tid
    }
  })
}

// 流程图详情
export function fetchassignTaskDetail(id) {
  return request({
    url: `/task/emergency_command_task/processDetails/${id}`,
    method: 'get'
  })
}

// 获取反馈列表
export function getReplyList(replyTaskId, departmentId, accountId) {
  return request({
    url: '/task/emergency_command_task/replyList',
    method: 'post',
    data: {
      replyTaskId,
      departmentId,
      accountId
    }
  })
}
/**
 * @method functionName
 * @{taskType} 任务类型
 * @{eventTypeId} 事件类型
 * @{completeStatus} 完成状态
 * @{userId} 用户id
 * @{startDate} 开始日期
 * @{endDate} 结束日期
 * @description 导出
 */
export function exportExcel(taskType,
  emergencyLevel,
  completeStatus,
  userId,
  startDate,
  endDate,
  keywords,
  myCompleteStatus) {
  return request({
    url: '/task/export',
    method: 'get',
    responseType: 'blob',
    params: {
      taskType,
      emergencyLevel,
      completeStatus,
      userId,
      startDate,
      endDate,
      keywords,
      myCompleteStatus
    }
  })
}
