import request from '@/utils/request'

/** 分页 */
export function getList(data) {
  return request({
    url: '/waterSource/behavior-analysis-setting/page',
    method: 'post',
    data
  })
}
/** 行为类别 */
export function behaviorList() {
  return request({
    url: '/waterSource/behavior-analysis-setting/group',
    method: 'get'
  })
}

/** 新增 */
export function saveDispose(data) {
  return request({
    url: '/waterSource/behavior-analysis-setting/save',
    method: 'post',
    data
  })
}
/**  详情 */
export function detailDispose({ id }) {
  return request({
    url: `/waterSource/behavior-analysis-setting/detail?id=${id}`,
    method: 'get'
  })
}
/** 编辑 */
export function updateDispose(data) {
  return request({
    url: '/waterSource/behavior-analysis-setting/update',
    method: 'put',
    data
  })
}
/** 删除 */
export function deleteDispose({ id }) {
  return request({
    url: `/waterSource/behavior-analysis-setting/delete?ids=${id}`,
    method: 'DELETE'
  })
}
/** 一键复制 */
export function copyDispose(data) {
  return request({
    url: '/waterSource/behavior-analysis-setting/copy',
    method: 'post',
    data
  })
}
