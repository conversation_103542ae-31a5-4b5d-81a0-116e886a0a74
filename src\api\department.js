import request from '@/utils/request'

export function addDepartment(name) {
  return request({
    url: '/department/insertDepartment',
    method: 'post',
    data: {
      name
    }
  })
}

export function delDepartment(DepartmentId) {
  return request({
    url: `/department/deleteDepartment?id=${DepartmentId}`,
    method: 'post'
    // data: {
    //   id: DepartmentId
    // }
  })
}

export function updateDepartment(departmentId, departmentName) {
  return request({
    url: '/department/updateDepartment',
    method: 'post',
    data: {
      id: departmentId,
      name: departmentName
    }
  })
}

export function fetchDepartmentList(pageNum, pageSize) {
  return request({
    url: '/department/departmentList',
    method: 'post',
    data: {
      pageNum,
      pageSize
    }
  })
}

// export function fetchAllDepartmentList() {
//   return request({
//      url: '/department/list',
//     method: 'post',
//     data: {
//       id: undefined
//     }
//   })
// }

export function fetchAllDepartmentList() {
  return request({
    url: '/department/listDepartment',
    method: 'post'
  })
}

export function fetchUserByDepartment(level, id) {
  return request({
    url: `/department/getUserDepartment?level=${level}&id=${id}`,
    method: 'post'
  })
}

export function fetchDepartmentByUser(userId) {
  return request({
    url: '/user/departmentList',
    method: 'post',
    data: {
      id: userId
    }
  })
}

export function fetchDepartmentByUserId(id) {
  return request({
    url: '/department/list',
    method: 'post',
    data: {
      id
    }
  })
}
