import request from '@/utils/request'

/**
 * 获取声光设备列表
 * @param {Object} data {
    pageNum,
    pageSize
  }
 * @returns request
 */
export function getList(data) {
  const {
    pageNum, pageSize
  } = data

  return request({
    url: '/waterSource/audible-light/page',
    method: 'post',
    data: {
      pageNum: pageNum || 1,
      pageSize: pageSize || 10
    }
  })
}

/**
 * 新增声光设备
 * @param {Object} data 所有参数均为必填项
 * {
    "stationId": "站点id，从水质状况菜单中的站点获取",
    "name": "设备名称,60字符",
    "registerCode": "注册码，8位",
    "imei": "IMEI码，16位",
    "lng": "经度",
    "lat": "纬度"
}
 * @returns promise
 */
export function add(data) {
  const {
    stationId,
    stationName,
    name,
    registerCode,
    imei,
    lat,
    lng
  } = data

  return request({
    url: '/waterSource/audible-light/save',
    method: 'POST',
    data: {
      stationId,
      stationName,
      name,
      registerCode,
      imei,
      lat,
      lng
    }
  })
}

/**
 * 编辑设备
 * @param {Object} data 所有参数均为必填项
 * {
    "id": "设备id",
    "stationId": "站点id，从水质状况菜单中的站点获取",
    "name": "设备名称,60字符",
    "registerCode": "注册码，8位",
    "imei": "IMEI码，16位",
    "lng": "经度",
    "lat": "纬度"
}
 * @returns promise
 */
export function update(data) {
  const {
    id,
    stationId,
    stationName,
    name,
    registerCode,
    imei,
    lat,
    lng
  } = data

  return request({
    url: '/waterSource/audible-light/update',
    method: 'PUT',
    data: {
      id,
      stationId,
      stationName,
      name,
      registerCode,
      imei,
      lat,
      lng
    }
  })
}

/**
 * 删除设备
 * @param {String} id 设备id
 * @returns promise
 */
export function del(id) {
  return request({
    url: `/waterSource/audible-light/delete?id=${id}`,
    method: 'DELETE'
  })
}

/**
 * 设置音量大小
 * @param {String} imei 设备IMEI码
 * @param {String} value 音量，最小0，最大30
 * @returns promise
 */
export function setVolume(imei, value) {
  return request({
    url: `/waterSource/audible-light/settingVolume?imei=${imei}&value=${value}`,
    method: 'PUT'
  })
}

export default {}
