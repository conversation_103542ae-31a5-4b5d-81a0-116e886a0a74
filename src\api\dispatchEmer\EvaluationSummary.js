import request from '@/utils/request'
// ----------启动预案接口------------

/**
 * @description 启动预案列表
 * @needData data-分页查询数据
 * <AUTHOR>
 */
export function getPage(data) {
  return request({
    url: `/task/startPlanPage`,
    method: 'post',
    data
  })
}
/**
 * @description 新增任务
 * @needData data-任务数据
 * <AUTHOR>
 */
export function doSave(data) {
  return request({
    url: `/task/createTask`,
    method: 'post',
    data,
    timeout: 30000
  })
}
/**
 * @description 修改任务
 * @needData data-任务数据
 * <AUTHOR>
 */
export function doUpdate(data) {
  return request({
    url: `/task/updatePlan`,
    method: 'put',
    data
  })
}
/**
 * @description 删除任务
 * @needData id-任务id
 * <AUTHOR>
 */
export function doDelete(id) {
  return request({
    url: `/task/delete`,
    method: 'post',
    data: {
      taskId: id
    }
  })
}
/**
 * @description 关闭任务
 * @needData data- eventId, userId, userName
 * <AUTHOR>
 */
export function closeEmergency(data) {
  return request({
    url: '/task/close',
    method: 'post',
    data
  })
}
/**
 * @description 获取预案列表
 * @needData data-任务数据
 * <AUTHOR>
 */
export function getPlanList(level) {
  return request({
    url: `/task/plan/getPlanList?planType=3&level=${level}`,
    method: 'get'
  })
}
