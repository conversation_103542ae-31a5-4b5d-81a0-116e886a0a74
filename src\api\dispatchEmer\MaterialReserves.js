import request from '@/utils/request'
// ----------物资管理接口------------

/**
 * @description 仓库列表
 * @needData data-分页查询数据
 * <AUTHOR>
 */
export function getPage(data) {
  return request({
    url: `/waterSource/storage/page`,
    method: 'post',
    data
  })
}
/**
 * @description 新增仓库
 * @needData data-仓库数据
 * <AUTHOR>
 */
export function doSave(data) {
  return request({
    url: `/waterSource/storage/save`,
    method: 'post',
    data
  })
}
/**
 * @description 修改仓库
 * @needData data-仓库数据
 * <AUTHOR>
 */
export function doUpdate(data) {
  return request({
    url: `/waterSource/storage/update`,
    method: 'put',
    data
  })
}
/**
 * @description 删除仓库
 * @needData id-仓库id
 * <AUTHOR>
 */
export function dodDlete(id) {
  return request({
    url: `/waterSource/storage/delete?id=${id}`,
    method: 'delete'
  })
}

