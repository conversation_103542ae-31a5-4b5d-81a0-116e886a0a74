/* eslint-disable */
import request from "@/utils/request";
import qs from "qs";
// 应急事件列表
export function emergencyList(
  pageNum,
  pageSize,
  userId,
  eventCategory,
  queryStartTime,
  queryEndTime,
  eventStatus
) {
  return request({
    url: "/task/emergency/list",
    method: "post",
    data: {
      pageNum,
      pageSize,
      userId,
      eventCategory,
      queryStartTime,
      queryEndTime,
      eventStatus
    }
  });
}
// 新增应急事件
export function addEmergency(
  userId,
  userName,
  departmentName,
  departmentId,
  eventCategory,
  title,
  startTime,
  isLinear,
  descriptionList,
  content,
  baseAnnexList
) {
  return request({
    url: "/task/emergency",
    method: "post",
    timeout: 60000,
    data: {
      userId,
      userName,
      departmentName,
      departmentId,
      eventCategory,
      title,
      startTime,
      isLinear,
      descriptionList,
      content,
      baseAnnexList
    }
  });
}
// 创建子事件
export function createChildEmergency(
  eventParentId,
  userId,
  userName,
  departmentName,
  departmentId,
  title,
  startTime,
  endTime,
  isLinear,
  descriptionList,
  content,
  baseAnnexList,
  eventCategory,
  totalEventId
) {
  return request({
    url: "/task/emergency/create-child",
    method: "post",
    timeout: 60000,
    data: {
      eventParentId,
      userId,
      userName,
      departmentName,
      departmentId,
      title,
      startTime,
      endTime,
      isLinear,
      descriptionList,
      content,
      baseAnnexList,
      eventCategory,
      totalEventId
    }
  });
}
// 更新应急处理事件
export function updateEmergency(
  eventId,
  startTime,
  endTime,
  userId,
  userName,
  isLinear,
  descriptionList
) {
  return request({
    url: "/task/emergency",
    method: "put",
    data: {
      eventId,
      startTime,
      endTime,
      userId,
      userName,
      isLinear,
      descriptionList
    }
  });
}
// 获取事件详情
export function emergencyDetails(eid, userId) {
  return request({
    url: `/task/emergency/event/details`,
    method: "get",
    params: {
      eid,
      userId
    }
  });
}
// 开始事件
export function startEmergency(eventId, userId, userName) {
  return request({
    url: "/task/emergency/start",
    method: "post",
    data: {
      eventId,
      userId,
      userName
    }
  });
}
// 完成事件
export function completeEmergency(
  eventId,
  userId,
  userName,
  remark,
  baseAnnexList
) {
  return request({
    url: "/task/emergency/complete",
    method: "post",
    timeout: 60000,
    data: {
      eventId,
      userId,
      userName,
      remark,
      baseAnnexList
    }
  });
}
// 关闭任务
export function closeEmergency(eventId, userId, userName) {
  return request({
    url: "/task/emergency/close",
    method: "post",
    data: {
      eventId,
      userId,
      userName
    }
  });
}
// 执行部门选择列表
export function departmentList(departmentId, deptId) {
  return request({
    url: `/system/department/optionList`,
    method: "post",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    data: qs.stringify({
      departmentId,
      deptId
    })
  });
}
/* 联动预案 */
// 获取联动预案列表
export function getReservePlan(pageNum, pageSize, accountId) {
  return request({
    url: "/task/joint_law_linkage_plan/page_list",
    method: "post",
    data: {
      pageNum,
      pageSize,
      accountId
    }
  });
}
// 新增应急预案
export function addReservePlan(data) {
  return request({
    url: "/task/joint_law_linkage_plan",
    method: "post",
    data
  });
}
// 更新应急预案
export function updateReservePlan(
  planTitle,
  planId,
  isLine,
  actionList,
  ccList
) {
  return request({
    url: "/task/joint_law_linkage_plan",
    method: "put",
    data: {
      planTitle,
      planId,
      isLine,
      actionList,
      ccList
    }
  });
}
// 删除应急预案
export function delReservePlan(planId) {
  return request({
    url: `/task/joint_law_linkage_plan/${planId}`,
    method: "delete"
  });
}
// 事件处理流程图
export function eventProcessFlow(eid) {
  return request({
    url: `/task/emergency/eventProcessFlow/${eid}`,
    method: "get"
  });
}
// 日志列表
export function eventLogList(eventId) {
  return request({
    url: `/task/emergency/event_log_list/${eventId}`,
    method: "get"
  });
}
// 获取指派内容详情(点击流程图时显示)
export function processDetails(eventId) {
  return request({
    url: `/task/emergency/processDetails/${eventId}`,
    method: "get"
  });
}
// 指派任务执行人下拉列表
export function departmentOptionList(departmentId) {
  return request({
    url: `/task/system/department/child_task_optionList/${departmentId}`,
    method: "get"
  });
}
// 获取完成事件反馈详情列表
export function emergencyDetailsList(eventId) {
  return request({
    url: `/task/emergency/${eventId}`,
    method: "get"
  });
}
// 获取预案列表
export function getJointPlanPage(
  pageNum,
  pageSize,
  planType,
  stationTypeId,
  userId,
  stationId
) {
  return request({
    url: `/task/plan/getPlanPage`,
    method: "get",
    params: {
      pageNum,
      pageSize,
      planType,
      stationTypeId,
      userId,
      stationId
    }
  });
}
// 根据上级部门id查询部门集合
export function getDepartmentList(departmentId) {
  return request({
    url: `/system/department/list`,
    method: "get",
    params: {
      departmentId
    }
  });
}
// 获取部门列表
export function getDepartmentLists() {
  return request({
    url: `/system/department/getTopDepartmentList`,
    method: "get"
  });
}
// 获取人员列表
export function getUserList(departmentId) {
  return request({
    url: `/system/user/list`,
    method: "get",
    params: {
      departmentId
    }
  });
}
// 创建预案
export function createPlan(data) {
  return request({
    url: `/task/plan/createPlan`,
    method: "post",
    data
  });
}
// 修改预案
export function updatePlan(data) {
  return request({
    url: `/task/plan/updatePlan`,
    method: "post",
    data
  });
}
// 删除预案
export function deletePlan(data) {
  return request({
    url: `/task/plan/deletePlan`,
    method: "post",
    data
  });
}
// 预案开启
export function switchChange(data) {
  return request({
    url: `/task/plan/enable`,
    method: "post",
    data
  });
}
export function getList() {
  return request({
    url: `/system/department/getTopTaskExecutorList`,
    method: "get"
  });
}

export function getPlanType() {
  return request({
    url: `/task/plan/getPlanType`,
    method: "get"
  });
}
