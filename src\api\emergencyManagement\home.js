import request from '@/utils/request'
// ----------首页接口------------

/**
 * @description 任务完成情况统计图
 * @needData 无
 * <AUTHOR>
 */
export function taskStat() {
  return request({
    url: `/task/main/taskStat`,
    method: 'get'
  })
}
/**
 * @description 任务分类统计
 * @needData data- 1日 2月 3年
 * <AUTHOR>
 */
export function alarmStat(data) {
  return request({
    // url: `/task/main/alarmStat?type=${data || 3}`,
    url: '/task/main/taskGroupStat',
    method: 'get'
  })
}
/**
 * @description 新闻动态、双公示栏、水源文化
 * @needData data- 1->新闻动态 2->双公示栏 3->水源文化
 * <AUTHOR>
 */
export function information(data) {
  return request({
    url: `/task/main/information?type=${data}`,
    method: 'get'
  })
}
/**
 * @description 通知公告（默认返回3条）
 * @needData 无
 * <AUTHOR>
 */
export function noticeList() {
  return request({
    url: `/task/main/notice`,
    method: 'get'
  })
}
/**
 * @description 已办日历
 * @needData date - 月份（2022-01）
 * <AUTHOR>
 */
export function monthDoList(date) {
  return request({
    url: `/task/main/month/done?time=${date}`,
    method: 'get'
  })
}
/**
 * @description 获取待做任务数-分组
 * @needData 无
 * <AUTHOR>
 */
export function taskGroup() {
  return request({
    url: `/task/main/task/group`,
    method: 'get'
  })
}
/**
 * @description 待做任务列表
 * @needData data-分页查询参数
 * @taskType 1 联动, 2 日常, 3 应急, 4 异常
 * <AUTHOR>
 */
export function taskList(taskType) {
  return request({
    url: `/task/main/task`,
    method: 'post',
    data: {
      pageNum: 1,
      pageSize: 9999,
      query: {
        taskType
      }
    }
  })
}
