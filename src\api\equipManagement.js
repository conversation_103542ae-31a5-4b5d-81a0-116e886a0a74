import request from '@/utils/request'

// 获取水质站点table
export function findWaterStationList(data) {
  return request({
    url: '/management/water/findWaterStationList',
    method: 'post',
    data
  })
}

// 获取空气站点table
export function getAirList(data) {
  return request({
    url: '/air/air_station/page-list',
    method: 'post',
    data
  })
}

// 获取空气站点类型列表
export function getStationTypeData() {
  return request({
    url: '/air/station/type/list',
    method: 'get'
  })
}

// 获取空气站点 街道列表
export function getStreetData() {
  return request({
    url: '/air/area/street',
    method: 'get'
  })
}
// 获取空气站点阈值数据
export function getAirSitewarnData() {
  return request({
    url: '/air/alarm/item/list',
    method: 'get'
  })
}

// 修改空气站点预警值
export function editAirSitewarnData(data) {
  return request({
    url: '/air/alarm/item',
    method: 'put',
    data
  })
}

// 获取水质站点预警列表
export function getWatersiteData(data) {
  return request({
    url: '/management/water/getWaterAlarmItemByStationId',
    method: 'post',
    data
  })
}

// 获取水质站点表
export function getAllStationRealTimeRecordList(data) {
  return request({
    url: '/water/monitor/getAllStationRealTimeRecordList',
    method: 'get',
    params: data
  })
}

// 修改水质站点预警值
export function editWaterSitewarnData(data) {
  return request({
    url: '/water/management/updateWaterAlarmItemByStationId',
    method: 'post',
    data
  })
}

// 获取空气首页数据展示
export function airDataPresentation(data) {
  return request({
    url: '/air/air_station/air_home/data_show',
    method: 'get',
    params: data
  })
}

// 实时数据
export function realTimeData(data) {
  return request({
    url: '/air/air_station/getStationMonitor',
    method: 'get',
    params: data
  })
}

// 日历
export function calendar(data) {
  return request({
    url: '/air/air-county-day-aqi/monthAqiCalendar',
    method: 'get',
    params: data
  })
}

// 24小时空气质趋势
export function airTrend(data) {
  return request({
    url: '/air/air_station/aqi_trend',
    method: 'get',
    params: data
  })
}

// 获取水质首页数据展示
export function waterDataPresentation(data) {
  return request({
    url: '/water/station/getItemStationList',
    method: 'get',
    params: data
  })
}

// 水质实时数据
export function waterrealTimeData(data) {
  return request({
    url: '/water/station/getMonitorDetails',
    method: 'get',
    params: data
  })
}

// 水质24小时趋势
export function waterTrend(data) {
  return request({
    url: '/water/station/getHourMonitorItemList',
    method: 'get',
    params: data
  })
}

// 暂无视频
export function videoBinding(data) {
  return request({
    url: '/water/water-camera/untieStation',
    method: 'post',
    data
  })
}

// 获取水质监控列表
export function videoList(data) {
  return request({
    url: '/water/water-camera/listMonitor',
    method: 'post',
    data
  })
}
// 获取水质河流
export function getRiverList(data) {
  return request({
    url: `/water/monitor/getRiverList?districtCode=${data}`,
    method: 'get'
  })
}

// 水质视频绑定
export function waterVideoBinding(data) {
  return request({
    url: '/water/water-camera/bindStation',
    method: 'post',
    data
  })
}
// 水质视频解除绑定
export function untieStation(data) {
  return request({
    url: '/water/water-camera/untieStation',
    method: 'post',
    data
  })
}

// 获取水监播放
export function getWaterLive(id) {
  return request({
    url: `/water/water-camera/live/${id}`,
    method: 'get'
  })
}

// 获取站点信息
export function getStationCode(stationCode) {
  return request({
    url: `/air/air_station/getDetails?stationCode=${stationCode}`,
    method: 'get'
  })
}
// 获取水量站点信息
export function getWaterGauge() {
  return request({
    url: `/waterSource/waterGauge/list`,
    method: 'get'
  })
}
// 修改水量站点阈值
export function getWaterGaugeUpdate(data) {
  return request({
    url: `/waterSource/waterGauge/update`,
    method: 'put',
    data
  })
}
