import request from '@/utils/request'

/**
 *
 * @param {*} data 对象
 * @returns
 */
export function expertSave(data) {
  return request({
    url: '/waterSource/expert/save',
    method: 'post',
    data
  })
}

/**
 *
 * @param {*} data 分页
 * @returns
 */
export function getexpertList(data) {
  return request({
    url: '/waterSource/expert/page',
    method: 'post',
    data
  })
}

/**
 *
 * @param {*} data 修改
 * @returns
 */
export function updateInfo(data) {
  return request({
    url: '/waterSource/expert/update',
    method: 'put',
    data
  })
}
/**
 *
 * @param {*} id 专家id
 * @returns
 */
export function deleteInfo(id) {
  return request({
    url: `/waterSource/expert/delete?id=${id}`,
    method: 'delete'
  })
}
