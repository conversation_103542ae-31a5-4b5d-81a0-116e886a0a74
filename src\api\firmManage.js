import request from '@/utils/request'

// 分页查询企业
export function firmPageList(params) {
  return request({
    url: '/water/sewage/enter/pageList',
    method: 'get',
    params
  })
}

// 获取企业详情
export function firmDetail(id) {
  return request({
    url: '/water/sewage/enter/getDetail',
    method: 'get',
    params: { id }
  })
}

// 导出企业信息
export function firmExport(params) {
  return request({
    url: '/water/sewage/enter/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
