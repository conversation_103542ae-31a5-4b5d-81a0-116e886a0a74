import request from '@/utils/request'

export function addGroup(groupName, departmentId) {
  return request({
    url: '/api/vi/group/insertGroup',
    method: 'post',
    data: {
      groupName,
      departmentId
    }
  })
}

export function delGroup(groupId) {
  return request({
    url: `/api/vi/group/deleteGroup?id=${groupId}`,
    method: 'post'
  })
}

export function updateGroup(groupId, groupName, departmentId) {
  return request({
    url: '/api/vi/group/updateGroup',
    method: 'post',
    data: {
      id: groupId,
      groupName,
      departmentId
    }
  })
}

export function fetchGroupList(pageNum, pageSize) {
  return request({
    url: '/api/vi/group/groupList',
    method: 'post',
    data: {
      pageNum,
      pageSize
    }
  })
}

export function fetchAllGroupList() {
  return request({
    url: '/api/vi/group/list',
    method: 'post'
  })
}
