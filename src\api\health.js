import request from '@/utils/request'

export function riverList() {
  return request({
    url: '/water/river/list',
    method: 'get'
  })
}

export function riverWaterLevelPageList(params) {
  return request({
    url: '/water/river_water_level/pageList',
    method: 'get',
    params
  })
}

export function riverWaterLevelSave(data) {
  return request({
    url: '/water/river_water_level/save',
    method: 'post',
    data
  })
}

export function riverWaterLevelUpdate(data) {
  return request({
    url: '/water/river_water_level/update',
    method: 'put',
    data
  })
}

export function riverWaterLevelDel(id) {
  return request({
    url: `/water/river_water_level/del?id=${id}`,
    method: 'delete'
  })
}

export function warterEnvDataPageList(params) {
  return request({
    url: `/water/river-assessment-goal-month/pageList`,
    method: 'get',
    params
  })
}

export function goalMonthSave(data) {
  return request({
    url: `/water/river-assessment-goal-month/save`,
    method: 'post',
    data
  })
}

export function goalMonthUpdate(data) {
  return request({
    url: `/water/river-assessment-goal-month/update`,
    method: 'put',
    data
  })
}

export function goalMonthDel(id) {
  return request({
    url: `/water/river-assessment-goal-month/delete?id=${id}`,
    method: 'delete'
  })
}

export function goalMonthExport(params) {
  return request({
    url: `/water/river-assessment-goal-month/export`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}
