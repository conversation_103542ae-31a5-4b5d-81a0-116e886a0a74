import request from '@/utils/request'
/**
 * ! 获取工地站点列表
 * @param {*} streetCode 街道唯一码
 */
export function getCompanySiteList(streetCode) {
  return request({
    url: `/air/company/list`,
    method: 'GET',
    params: {
      streetCode
    }
  })
}
/**
 * 获取工地实时监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} companyId 工地 id*
 * @param {*} keywords 关键字*

 */
export function getCompanyNewList(args) {
  return request({
    url: '/air/company/page_list',
    method: 'post',
    data: args
  })
}
/**
 * 获取工地历史监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} keywords 关键字*
 * @param {*} companyId 工地 id*
 * @param {*} streetCode 街道 code*
 * @param {*} startDate 开始时间 code*
 * @param {*} endDate 结束时间 code*
 */
export function getCompanyOldList(args) {
  return request({
    url: '/air/company/pageListPollutant',
    method: 'post',
    data: args
  })
}

export function getWaterOfFiveOrSevenDetail(data) {
  return request({
    url: '/air/management/water/getWaterOfFiveOrSevenDetail',
    method: 'post',
    data
  })
}

/**
 * ! 获取印刷站点列表
 * @param {*} streetCode 街道唯一码
 */
export function getPrintSiteList(streetCode) {
  return request({
    url: '/water/print/list',
    method: 'get',
    params: {
      streetCode
    }
  })
}
/**
 * 获取印刷实时监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} keywords 关键字*
 * @param {*} printFactoryId 印刷厂 id*
 * @param {*} streetCode 街道 code*
 */
export function getPrintNewList(args) {
  return request({
    url: '/water/print/pageList',
    method: 'post',
    data: args
  })
}
/**
 * 获取印刷历史监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} keywords 关键字*
 * @param {*} printFactoryId 印刷厂 id*
 * @param {*} streetCode 街道 code*
 * @param {*} startDate 开始时间*
 * @param {*} endDate 结束时间*
 * @param {*} type 数据类型: 1 分钟数据 2 小时数据 3 天数据
 */
export function getPrintOldList(args) {
  return request({
    url: '/water/print/record/pageList',
    method: 'post',
    data: args
  })
}

/**
 * ! 获取汽修站点列表
 * @param {*} streetCode 街道唯一码
 */
export function getGarageSiteList(streetCode) {
  return request({
    url: '/water/garage/listByStreet',
    method: 'get',
    params: {
      streetCode
    }
  })
}
/**
 * 获取汽修实时监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} keywords 关键字*
 * @param {*} garageId 汽修厂id*
 * @param {*} streetCode 街道code*
 */
export function getGarageNewList(args) {
  return request({
    url: '/water/garage/pageList',
    method: 'post',
    data: args
  })
}
/**
 * 获取汽修历史监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} keywords 关键字*
 * @param {*} garageId 汽修厂id*
 * @param {*} streetCode 街道code*
 * @param {*} startDate 开始时间*
 * @param {*} endDate 结束时间*
 * @param {*} type 数据类型:1 分钟数据 2 小时数据 3 天数据
 */
export function getGarageOldList(args) {
  return request({
    url: '/water/garage/record/pageList',
    method: 'post',
    data: args
  })
}

/**
 * ! 获取餐饮站点列表
 * @param {*} streetCode 街道唯一码
 */
export function getRestaurantSiteList(streetCode) {
  return request({
    url: '/water/restaurant/listByStreet',
    method: 'get',
    params: {
      streetCode
    }
  })
}
/**
 * 获取餐饮实时监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} keywords 关键字*
 */
export function getRestaurantNewList(args) {
  return request({
    url: '/water/restaurant/page',
    method: 'post',
    data: args
  })
}
/**
 * 获取餐饮历史监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} keywords 关键字*
 * @param {*} restaurantName 餐馆名称*
 * @param {*} streetCode 街道code*
 * @param {*} startDate 开始时间*
 * @param {*} endDate 结束时间*
 * @param {*} type 数据类型: 1 分钟数据 2 小时数据 3 天数据
 */
export function getRestaurantOldList(args) {
  return request({
    url: '/water/restaurant/record/pageList',
    method: 'post',
    data: args
  })
}

/**
 * ! 获取加油站点列表
 * @param {*} streetCode 街道唯一码
 */
export function getGasSiteList(streetCode) {
  return request({
    url: '/water/gas/listByStreet',
    method: 'get',
    params: {
      streetCode
    }
  })
}
/**
 * 获取加油站实时监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} keywords 关键字*
 * @param {*} gasId 加油站id*
 */
export function getGasNewList(args) {
  return request({
    url: '/water/gas/page',
    method: 'post',
    data: args
  })
}
/**
 * 获取加油站历史监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} keywords 关键字*
 * @param {*} gasId 站点id*
 * @param {*} startDate 开始时间*
 * @param {*} endDate 结束时间*
 */
export function getGasOldList(args) {
  return request({
    url: '/water/gas/environment/pageList',
    method: 'post',
    data: args
  })
}

export function getGarageAllList(data) {
  return request({
    url: '/water/garage/web/list',
    method: 'get',
    data
  })
}
export function getPrintAllList(data) {
  return request({
    url: '/water/print/web/list',
    method: 'get',
    data
  })
}
export function getRestaurantAllList(data) {
  return request({
    url: '/water/restaurant/list',
    method: 'get',
    data
  })
}
export function getGasAllList(data) {
  return request({
    url: '/water/gas/list',
    method: 'get',
    data
  })
}
export function getHeavilyPollutingEnterpriseAllList() {
  return request({
    url: '/water/sewage/enterprise/listEnterprise',
    method: 'get'
  })
}
export function getSiteAllList(companyId) {
  return request({
    url: '/air/company/get',
    method: 'get',
    params: {
      companyId
    }
  })
}

// 印刷企业详情
export function getPrintingCardInfo(data) {
  return request({
    url: `/water/print/getPrintFactoryCardInfo?printFactoryId=${data}`,
    method: 'get'
  })
}
// 根据时间获取印刷工厂设备运行参数
export function getPrintingChange(printId, from, to) {
  return request({
    url: `/water/print/getPrintWeighingDetailsByDate?printId=${printId}&from=${from}&to=${to}`,
    method: 'get'
  })
}
// 根据时间获取印刷工厂排放详情数据
export function getPrintingChangeOne(printId, from, to) {
  return request({
    url: `/water/print/getPrintFactoryDetailsByDate?printId=${printId}&from=${from}&to=${to}`,
    method: 'get'
  })
}

// 在建工地
export function getConstructionList(pageNum, pageSize) {
  return request({
    url: '/air/company/getCompanyCardInfo',
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  })
}
// 在建工地导出
export function exportSiteTable(data) {
  return request({
    url: '/air/company-hour-data/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}
export function getConstructionCarList(data) {
  return request({
    url: `/air/company/getCompanyCardDetail?companyId=${data}`,
    method: 'get'
  })
}
// 根据时间范围获取工地报警监测信息
export function getHandleDateChange(data) {
  return request({
    url: '/air/company-hour-data/getMonitorInfoByDate',
    method: 'post',
    data
  })
}

// 获取加油站卡详情
export function getGasDetailInfo(gasId) {
  return request({
    url: `/water/gas/getGasCardDetailInfo?gasId=${gasId}`,
    method: 'get'
  })
}
// 根据时间获取加油站报警信息
export function getGasChange(gasId, from, to) {
  return request({
    url: `/water/gas/environment/getGasEnvironmentByDate`,
    method: 'get',
    params: {
      gasId,
      from,
      to
    }
  })
}

// 获取餐饮卡片详情
export function getRestaurantDetailInfo(restaurantId) {
  return request({
    url: `/water/restaurant/getRestaurantCardInfo?restaurantId=${restaurantId}`,
    method: 'get'
  })
}
// 根据日期获取餐饮报警信息
export function getRestaurantChange(restaurantName, from, to) {
  return request({
    url: `/water/restaurant/record/getRestaurantRecordDailyByDate?restaurantName=${restaurantName}&from=${from}&to=${to}`,
    method: 'get'
  })
}

// 获取汽修污染卡片详情数据
export function getGarageInfo(garageId) {
  return request({
    url: `/water/garage/getGarageCardInfo?garageId=${garageId}`,
    method: 'get'
  })
}
// 根据时间获取汽修报警数据
export function getAlarmChange(from, to, garageId) {
  return request({
    url: `/water/garage/findMonitorInfoByDate?from=${from}&to=${to}&garageId=${garageId}`,
    method: 'get'
  })
}

// 获取水 五 / 七厂 基本信息数据
export function getWaterOfFiveAndSevenBaseInfo(code) {
  return request({
    url: `/water/management/water/getWaterOfFiveAndSevenBaseInfo/${code}`,
    method: 'post'
  })
}

// 获取排口告警列表
export function outletAlarmList(data) {
  return request({
    url: '/water/management/alarm/pageList',
    method: 'post',
    data
  })
}

// 获取报警详情
export function outletAlarmDetial(alarmId) {
  return request({
    url: `/water/water-drain-alarm/getDetails/${alarmId}`,
    method: 'get'
  })
}

// 导出加油数据
export function exportGasList(params) {
  return request({
    url: `/water/gas/export`,
    method: 'get',
    responseType: 'blob',
    params
  })
}
// 获取重污印刷数据
export function getPrintList(data) {
  return request({
    url: `/water/print/record/listRecordByStation`,
    method: 'post',
    data
  })
}
// 导出重污印刷数据
export function exportPrintList(data) {
  return request({
    url: `/water/print/record/exportRecordByStation`,
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 获取重污汽修数据
export function getGarageList(data) {
  return request({
    url: `/water/garage/record/listRecordByStation`,
    method: 'post',
    data
  })
}
// 导出重污汽修数据
export function exportGarageList(data) {
  return request({
    url: `/water/garage/record/exportRecordByStation`,
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 导出重污汽修设备数据
export function exportGarageEquipList(data) {
  return request({
    url: `/water/garage/record/exportElectricityCount`,
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 查询汽车用电数据分析
export function getGarageElectric(data) {
  return request({
    url: `/water/garage/record/electricityCount`,
    method: 'post',
    timeout: 300000,
    data
  })
}

// 获取重污餐饮数据
export function getRestaurantList(data) {
  return request({
    url: `/water/restaurant/record/listRecordByStation`,
    method: 'post',
    data
  })
}
// 导出重污汽修数据
export function exportRestaurantList(data) {
  return request({
    url: `/water/restaurant/record/exportRecordByStation`,
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 查询印刷厂用电分析
export function getelectricList(data) {
  return request({
    url: `/water/print/record/electricityCount`,
    method: 'post',
    data
  })
}
// 导出印刷厂用电分析
export function exportElectricList(data) {
  return request({
    url: `/water/print/record/exportElectricityCount`,
    method: 'post',
    responseType: 'blob',
    data
  })
}

