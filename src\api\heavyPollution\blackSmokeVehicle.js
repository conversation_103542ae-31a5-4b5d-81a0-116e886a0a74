import request from '@/utils/request'

/**
 * 获取黑烟车列表
 */
// eslint-disable-next-line import/prefer-default-export
export function pageStation(params) {
  return request({
    url: '/water/traffic-flow-station/pageStation',
    method: 'get',
    params
  })
}

/**
 * 获取小时数据
 */
export function hourListRecord(params) {
  return request({
    url: '/water/trafficFlow/record/hour/listRecord',
    method: 'get',
    params
  })
}
/**
 * 获取天数据
 */
export function dayListRecord(params) {
  return request({
    url: '/water/trafficFlow/record/day/listRecord',
    method: 'get',
    params
  })
}
/**
 * 导出小时数据
 */
export function exportHourListRecord(params) {
  return request({
    url: '/water/trafficFlow/record/hour/exportRecord',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}

/**
 * 导出天数据
 */
export function exportDayListRecord(params) {
  return request({
    url: 'water/trafficFlow/record/day/exportRecord',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}
