import request from '@/utils/request'

/**
 * 分页获取排污企业
 */
// eslint-disable-next-line import/prefer-default-export
export function pageStation(params) {
  return request({
    url: '/water/sewage/enterprise/pageList',
    method: 'get',
    params
  })
}
/**
 * 获取排污企业列表
 * @param {*} params
 * @returns
 */
export function listStation(params) {
  return request({
    url: '/water/sewage/enterprise/list',
    method: 'get',
    params
  })
}

/**
 * 获取历史数据
 */
export function dayListRecord(params) {
  return request({
    url: '/water/heavily-polluting-water/day/web/ListRecord',
    method: 'get',
    params
  })
}
/**
 * 获取历史数据
 */
export function hourListRecord(params) {
  return request({
    url: '/water/heavily-polluting-water/hour/web/ListRecord',
    method: 'get',
    params
  })
}
/**
 * 导出天数据
 */
export function exportDayListRecord(params) {
  return request({
    url: '/water/heavily-polluting-water/day/web/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}
/**
 * 导出小时数据
 */
export function exportHourListRecord(params) {
  return request({
    url: '/water/heavily-polluting-water/hour/web/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}
/**
 * 获取排污企业列表
 * @param {*} params
 * @returns
 */
export function listStationRank(params) {
  return request({
    url: '/water/sewage/enterprise/ranking',
    method: 'get',
    params
  })
}
