import request from '@/utils/request'

/**
 * 获取电量列表
 */
// eslint-disable-next-line import/prefer-default-export
export function pageStation(params) {
  return request({
    url: '/water/electricity/pageStation',
    method: 'get',
    params
  })
}

/**
 * 获取小时历史数据
 */
export function webHourListRecord(params) {
  return request({
    url: '/water/electricity/record/webHourListRecord',
    method: 'get',
    params
  })
}
/**
 * 获取天历史数据
 */
export function webDayListRecord(params) {
  return request({
    url: '/water/electricity/record/webDayListRecord',
    method: 'get',
    params
  })
}
/**
 * 导出天数据
 */
export function exportDayListRecord(params) {
  return request({
    url: '/water/electricity/record/exportDayListRecord',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}
/**
 * 导出小时数据
 */
export function exportHourListRecord(params) {
  return request({
    url: '/water/electricity/record/exportHourListRecord',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}
