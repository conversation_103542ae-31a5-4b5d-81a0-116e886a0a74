import request from '@/utils/request'

/**
 * 获取有雷达图的日期
 */
// eslint-disable-next-line import/prefer-default-export
export function listExistDate(findDate) {
  return request({
    url: `/air/web/lidar-scanning-record/listExistDate?findDate=${findDate}`,
    method: 'get'
  })
}
// 获取指定点位的污染情况
export function findPoint(params) {
  return request({
    url: `/air/web/lidar-scanning-record/findPoint`,
    method: 'get',
    params
  })
}
// 获取指定点位的污染企业指纹库列表
export function listStation(params) {
  return request({
    url: `/air/web/lidar-scanning-record/listStation`,
    method: 'get',
    params
  })
}
// 获取雷达扫描列表
export function dataRedList(date) {
  return request({
    url: `/air/web/lidar-scanning-record/dataFileList?findDate=${date}`,
    method: 'get'
  })
}
// 获取指定点位的监测趋势
export function monitorTrend(params) {
  return request({
    url: `/air/web/lidar-scanning-record/monitorTrend`,
    method: 'get',
    params
  })
}
// 获取告警列表
export function alarmCount(params) {
  return request({
    url: `/air/web/lidar-scanning-record/alarmCount`,
    method: 'get',
    params
  })
}
// 获取污染源指纹库的监测参数列表
export function getStationMonitorItem(params) {
  return request({
    url: `/air/web/lidar-scanning-record/getStationMonitorItem`,
    method: 'get',
    params
  })
}
// 获取污染源指纹库的监测参数列表
export function getLastView() {
  return request({
    url: `/air/web/lidar-scanning-record/getLastView?type=1`,
    method: 'get'
  })
}
