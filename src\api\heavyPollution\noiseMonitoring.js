import request from '@/utils/request'

/**
 * 获取噪声站点列表
 */
// eslint-disable-next-line import/prefer-default-export
export function webListStation(params) {
  return request({
    url: '/water/noise/webListStation',
    method: 'get',
    params
  })
}
/**
 * 获取分钟历史数据
 */
export function webMinutesDataAnalyze(params) {
  return request({
    url: '/water/noise-record/webMinutesDataAnalyze',
    method: 'get',
    params
  })
}
/**
 * 获取小时历史数据
 */
export function webHourDataAnalyze(params) {
  return request({
    url: '/water/noise-record/webHourDataAnalyze',
    method: 'get',
    params
  })
}
/**
 * 获取天历史数据
 */
export function webDataDaysAnalyze(params) {
  return request({
    url: '/water/noise-record/webDataDaysAnalyze',
    method: 'get',
    params
  })
}
/**
 * 获取天历史数据
 */
export function exportRecordList(params) {
  return request({
    url: '/water/noise-record/exportRecordList',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}

/**
 * 获取报表数据
 */
export function webReportList(params) {
  return request({
    url: '/water/noise-record/noiseReport',
    method: 'get',
    params
  })
}
/**
 * 噪声占比分析
 */
export function proportionAnalyze(params) {
  return request({
    url: '/water/noise/proportionAnalyze',
    method: 'get',
    params
  })
}
/**
 * 噪声日历
 */
export function calendar(params) {
  return request({
    url: '/water/noise/calendar',
    method: 'get',
    params
  })
}
/**
 * 噪声日历
 */
export function ranking() {
  return request({
    url: '/water/noise/ranking',
    method: 'get'
  })
}
