import request from '@/utils/request'

/**
 * 获取固废列表
 */
// eslint-disable-next-line import/prefer-default-export
export function pageStation(params) {
  return request({
    url: '/water/waste-solid-device/pageStation',
    method: 'get',
    params
  })
}

/**
 * 获取入库历史数据
 */
export function listInRecord(params) {
  return request({
    url: '/water/waste/record/web/listInRecord',
    method: 'get',
    params
  })
}
/**
 * 获取出库天历史数据
 */
export function listOutRecord(params) {
  return request({
    url: '/water/waste/record/web/listOutRecord',
    method: 'get',
    params
  })
}
/**
 * 获取出库天历史数据
 */
export function listStockRecord(params) {
  return request({
    url: '/water/waste/record/web/listStockRecord',
    method: 'get',
    params
  })
}
/**
 * 导出天数据
 */
export function exportListRecord(params) {
  return request({
    url: '/water/waste/record/web/exportListRecord',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}
