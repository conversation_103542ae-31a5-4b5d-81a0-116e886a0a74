import request from '@/utils/request'

/**
 * @method functionName
 * @param {type} data 说明
 * @description 排口监测历史记录小时
 */
export function getHourListData(params) {
  return request({
    url: '/water/drain/monitorRecord/page',
    method: 'get',
    params
  })
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 排口监测历史记录日
 */
export function getListData(params) {
  return request({
    url: '/water/drain/monitorRecord/statisticsPage',
    method: 'get',
    params
  })
}
/**
 * @method functionName
 * @param {type} data 说明
 * @description 获取河流
 */
export function getRiverList() {
  return request({
    url: '/water/river/listByDrain',
    method: 'get'
  })
}

