import request from '@/utils/request'

// 获取一周未来天气
export function getWeekWeather() {
  return request({
    url: '/air/weather/week',
    method: 'get'
  })
}

// 污染物
export function getHpeSum() {
  return request({
    url: '/heavilyPollutingEnterprise/getHpeSum',
    method: 'get'
  })
}

// 获取重污工地列表
export function getCompanyList() {
  return request({
    url: '/air/company/list',
    method: 'get'
  })
}

// 获取重污工地列表
export function getHeavilyPollutingEnterpriseList() {
  return request({
    url: '/water/monitor/getHeavilyPollutingEnterpriseList?districtCode=510106',
    method: 'get'
  })
}
// 管理端-首页获取温度和水流量
export function getList() {
  return request({
    url: '/water/monitor/getStationInfo?districtCode=510106',
    method: 'get'
  })
}
/**
 * @method functionName
 * @param {gasId} 加油站id
 * @description 根据加油站获取加油机和加油枪数据信息
 */
export function getFuelDispenserByGasId(gasId) {
  return request({
    url: `/water/fuel/dispenser/getFuelDispenserByGasId/${gasId}`,
    method: 'get'
  })
}
