// 排口监测接口

import request from '@/utils/request'

// 分页查询智能抓拍
export function cameraPageList(data) {
  return request({
    url: '/water/water-camera-alarm/pageList',
    method: 'post',
    data
  })
}

// 新增智能抓拍
export function cameraSave(data) {
  return request({
    url: '/water/water-camera-alarm/save',
    method: 'post',
    data
  })
}

// 获取抓拍摄像头列表
export function getCaptureList(data) {
  return request({
    url: '/water/drain-station/cameraList',
    method: 'post',
    data
  })
}

// 获取摄像头抓拍记录列表
export function getOuterCamera(data) {
  return request({
    url: '/water/water-camera-smart-snap/pageList',
    method: 'post',
    data
  })
}

// 获取河流站点列表
export function getWaterList() {
  return request({
    url: '/water/river/list',
    method: 'get'
  })
}

// 根据河流站点 获取排口站点列表
export function getOutletStationList(riverId) {
  return request({
    url: `/water/drain-station/listByRiver?riverId=${riverId}`,
    method: 'get'
  })
}

// 新增智能抓拍
export function newSmartCapture(data) {
  return request({
    url: '/water/water-camera-alarm/save',
    method: 'post',
    data
  })
}

// 根据告警id获取详情
export function getAlarmDetails(alarmId) {
  return request({
    url: `/water/water-camera-alarm/getDetails/${alarmId}`,
    method: 'get'
  })
}

// 获取排口站点
export function getOutletStation(alarmId) {
  return request({
    url: '/water/drain-station/list',
    method: 'get'
  })
}

// 获取设备分页列表
export function getDeviceList(
  pageNum,
  pageSize,
  orderParam,
  waterStationId,
  drainStationId,
  drainId,
  samplingStatus
) {
  return request({
    url: '/water/drain-sampler/getSamplerPage',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      orderParam,
      waterStationId,
      drainStationId,
      drainId,
      samplingStatus
    }
  })
}

// 获取采样记录分页列表
export function getSamplingRecordList(
  pageNum,
  pageSize,
  waterStationId,
  drainStationId,
  drainId,
  startTime,
  endTime
) {
  return request({
    url: '/water/drain-sampler/getSampleRecordPage',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      waterStationId,
      drainStationId,
      drainId,
      startTime,
      endTime
    }
  })
}

// 根据排口id 获取排口监测数据信息
export function getDischargeMonitoring(drainId) {
  return request({
    url: `/water/water-drain-sampler-monitor-record/getMonitorRecordCount/${drainId}`,
    method: 'get'
  })
}

// 取样监测
// 获取化验记录分页列表
export function sampleDataList(
  pageNum,
  pageSize,
  riverId,
  drainId,
  startDate,
  endDate
) {
  return request({
    url: '/water/drain/assayRecord/page',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      riverId,
      drainId,
      startDate,
      endDate
    }
  })
}

// 根据河流ID获取排口列表
export function outletList(riverId) {
  return request({
    url: `/water/drain/listByRiverId?riverId=${riverId}`,
    method: 'get'
  })
}

// 获取未绑定化验记录的采样记录列表
export function sampleNumberList111(drainId, samplingDate) {
  return request({
    url: `/water/drain/samplingRecord/getUnboundAssayList?drainId=${drainId}&samplingDate=${samplingDate}`,
    method: 'get'
  })
}

// 录入样本
export function sampleDetermination(data) {
  return request({
    url: '/water/drain/assayRecord/create',
    method: 'post',
    data
  })
}

// 获取采样记录分页列表
export function operationDetailsList(
  pageNum,
  pageSize,
  riverId,
  drainId,
  startDate,
  endDate
) {
  return request({
    url: '/water/drain/samplingRecord/page',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      riverId,
      drainId,
      startDate,
      endDate
    }
  })
}

// 设备管理
// 获取排口分页列表
export function outletEquipmentList(pageNum, pageSize, riverId, drainId, isOnline) {
  return request({
    url: '/water/drain/page',
    method: 'get',
    params: { pageNum, pageSize, riverId, drainId, isOnline }
  })
}

// 智能抓拍
// 获取摄像头抓拍记录列表
export function captureList(data) {
  return request({
    url: '/water/water-camera-smart-snap/pageList',
    method: 'post',
    data
  })
}

// 修改抓拍记录
export function changeCaptureRecord(data) {
  return request({
    url: '/water/water-camera-smart-snap/updateSmartSnap',
    method: 'put',
    data
  })
}

// 获取排户分页列表
export function getHouseholdList(
  pageNum,
  pageSize,
  drainId,
  merchantTypeId
) {
  return request({
    url: '/water/drain/merchant/page',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      drainId,
      merchantTypeId
    }
  })
}

// 获取排户采样记录分页列表
export function fingerprintInformationList(
  pageNum,
  pageSize,
  merchantId
) {
  return request({
    url: '/water/drain/merchant/recordPage',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      merchantId
    }
  })
}

// 添加样本记录
export function addSampleRecord(data) {
  return request({
    url: '/water/drain/merchant/createRecord',
    method: 'post',
    data
  })
}

// 编辑排户
export function editHousehold(data) {
  return request({
    url: '/water/drain/merchant/update',
    method: 'post',
    data
  })
}

// 排户录入指纹
export function inputFingerprintList(data) {
  return request({
    url: '/water/drain/merchant/create',
    method: 'post',
    data
  })
}

// 获取排口列表接口
export function fingerprintOutletList() {
  return request({
    url: '/water/drain/bigData/listDrain',
    method: 'get'
  })
}

// 在线监测 首页站点列表
export function getOnlineMonitoring(
  keywords,
  status,
  order
) {
  return request({
    url: '/water/drain/managementListDrain',
    method: 'get',
    params: {
      keywords,
      status,
      order
    }
  })
}

// 根据排口id获取排口详情数据 监测数据，监控数据
export function getAllOutletDetails(drainId) {
  return request({
    url: '/water/drain/managementGetDrainDetail',
    method: 'get',
    params: {
      drainId
    }
  })
}

// 获取水质站点列表
export function getWaterSiteList(riverId) {
  return request({
    url: '/water/station/listByRiver',
    method: 'get',
    params: {
      riverId
    }
  })
}

// 获取地面监控列表
export function getGroundMonitoringList() {
  return request({
    url: '/water/drain/camera/listOuter',
    method: 'get'
  })
}

// 获取管网监控列表
export function getOfficialMonitoringList(drainId) {
  return request({
    url: '/water/drain/camera/listNoBindDrainInner',
    method: 'get',
    params: {
      drainId
    }
  })
}

// 新建排口
export function addBuildOutlet(data) {
  return request({
    url: '/water/drain/save',
    method: 'post',
    data
  })
}

// 更新排口
export function editBuildOutlet(data) {
  return request({
    url: '/water/drain/update',
    method: 'put',
    data
  })
}

// 导出排口列表
export function excelOutletList(data) {
  return request({
    url: '/water/drain/exportDrain',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 获取排户列表接口
export function getHouseholdSchedulingList(keywords) {
  return request({
    url: '/water/drain/merchant/management/listByKeywords',
    method: 'get',
    params: {
      keywords
    }
  })
}

// 获取排户详情数据
export function getHouseholdSchedulingDetial(id) {
  return request({
    url: '/water/drain/merchant/management/getDetailById',
    method: 'get',
    params: {
      id
    }
  })
}

// 根据排口查询绑定的监控列表
export function getWaterMonitoringList(drainId) {
  return request({
    url: `/water/drain/camera/listByDrain/${drainId}`,
    method: 'get'
  })
}

// 根据监控获取抓拍设置
export function getSnapSetList(monitorId) {
  return request({
    url: `/water/drain/snap/config/getByMonitor/${monitorId}`,
    method: 'get'
  })
}

// 设置排口抓拍
export function setOutletSnap(data) {
  return request({
    url: '/water/drain/snap/config/saveUpdate',
    method: 'post',
    data
  })
}

// 修改抓拍图片的识别识别框
export function modifyPickupDialog(data) {
  return request({
    url: '/water/water-camera-smart-snap/modifyImage',
    method: 'put',
    data
  })
}

// 设备重启
export function equipmentRestart(
  imei,
  executionType,
  userId
) {
  return request({
    url: '/water/drain/device/restart',
    method: 'get',
    params: {
      imei,
      executionType,
      userId
    }
  })
}

// 删除化验记录
export function deleteSamplingRecord(recordId) {
  return request({
    url: '/water/drain/assayRecord/delete',
    method: 'post',
    data: recordId
  })
}

// 更新化验记录
export function UpdateSamplingRecord(data) {
  return request({
    url: '/water/drain/assayRecord/update',
    method: 'post',
    data
  })
}

// 更新指纹库样本记录
export function UpdateFingerprintRecord(data) {
  return request({
    url: '/water/drain/merchant/updateRecord',
    method: 'post',
    data
  })
}

// 删除样本记录
export function deleteFingerprintRecord(recordId) {
  return request({
    url: '/water/drain/merchant/deleteRecord',
    method: 'post',
    data: recordId
  })
}

// 删除排户
export function deleteFingerprintMerchant(merchantId) {
  return request({
    url: '/water/drain/merchant/delete',
    method: 'post',
    data: merchantId
  })
}

// 删除排口
export function deleteOutletList(waterDrainId) {
  return request({
    url: '/water/drain/delete',
    method: 'delete',
    data: waterDrainId
  })
}

// 查询排口的告警阈值列表
export function getOutletWarningList(drainId) {
  return request({
    url: `/water/drain/alarm/item/listByDrain/${drainId}`,
    method: 'get'
  })
}

// 获取河流和河流下排口列表
export function getOutletRiversList(keywords) {
  return request({
    url: '/water/drain/listRiverAndDrainList',
    method: 'get',
    params: {
      keywords
    }
  })
}

// 一键修改阈值
export function onekeySetOutlet(data) {
  return request({
    url: '/water/drain/alarm/item/updateBatchItem',
    method: 'put',
    data
  })
}

// 修改告警阈值
export function modifyOutlet(data) {
  return request({
    url: '/water/drain/alarm/item/updateItem',
    method: 'put',
    data
  })
}

/**
 * 获取重污企业告警分页列表
 * @param {*} pageNum
 * @param {*} pageSize
 * @param {*} enterpriseId
 * @param {*} startDate
 * @param {*} endDate
 * @returns
 */
export function getHeavyPollutionEnterpriseList(
  pageNum,
  pageSize,
  enterpriseId,
  startDate,
  endDate
) {
  return request({
    url: '/heavilyPollutingEnterprise/getEnterpriseAlarmPage',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      enterpriseId,
      startDate,
      endDate
    }
  })
}

/**
 * 重污企业告警导出
 * @param {*} startDate
 * @param {*} endDate
 * @param {*} enterpriseId
 * @returns
 */
export function exportHeavyPollution(startDate, endDate, enterpriseId) {
  return request({
    url: `/heavilyPollutingEnterprise/exportEnterpriseAlarmList?startDate=${startDate}&endDate=${endDate}&enterpriseId=${enterpriseId}`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 获取水五厂或者七场企业信息
 * @returns
 */
export function getWaterEnterpriseList() {
  return request({
    url: '/heavilyPollutingEnterprise/getHpeWaterDetail',
    method: 'get'
  })
}

/**
 * 获取重污企业监测阈值列表
 * @param {*} enterpriseId
 * @returns
 */
export function getHeavyPollutionThresholdList(enterpriseId) {
  return request({
    url: `/heavilyPollutingEnterprise/getThresholdList?enterpriseId=${enterpriseId}`,
    method: 'get'
  })
}

/**
 * 修改重污企业监测阈值
 * @param {*} data
 * @returns
 */
export function modifyHeavyPollutionThreshold(data) {
  return request({
    url: '/heavilyPollutingEnterprise/updateThreshold',
    method: 'post',
    data
  })
}
