import request from '@/utils/request'

/**
 * 获取分页列表
 */
export function getPage(params) {
  return request({
    url: `/task/issues_manifest/doPage`,
    method: 'get',
    params
  })
}

/**
 * 级联获取 区县==》街道
 */
export function districtStreetTree(params) {
  return request({
    url: `/air/area/districtStreetTree`,
    method: 'get',
    params
  })
}

/**
 * 获取问题清单详情
 */
export function getDetail(params) {
  return request({
    url: `/task/issues_manifest/getDetail`,
    method: 'get',
    params
  })
}

/**
 * 导出问题清单列表
 */
export function exportDetail(params) {
  return request({
    url: `/task/issues_manifest/export`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 删除问题清单
 */
export function del(params) {
  return request({
    url: `/task/issues_manifest/del`,
    method: 'delete',
    params
  })
}

/**
 * 完成整改
 */
export function completeRectification(data) {
  return request({
    url: `/task/issues_manifest/completeRectification`,
    method: 'put',
    data
  })
}

/**
 * 创建任务
 */
export function addTask(data) {
  return request({
    url: '/task/issues_manifest/toTask',
    method: 'post',
    data
  })
}

/**
 * 通报情况修正
 */
export function notification(data) {
  return request({
    url: '/task/issues_manifest/notification',
    method: 'put',
    data
  })
}
/**
 *  获取问题清单类型
 */
export function getIsuueTypeList() {
  return request({
    url: `/task/issues_manifest_type/list`,
    method: 'get'
  })
}
