import request from '@/utils/request'
// 获取联动分页数据
// export function getLawListData(pageNum,
//   pageSize, taskType,
//   stationTypeId,
//   completeStatus,
//   startDate,
//   endDate,
//   eventTypeId,
//   userId,
//   keywords,
//   myCompleteStatus,
//   isPush) {
//   return request({
//     url: '/task/getTaskPage',
//     method: 'get',
//     params: {
//       pageNum,
//       pageSize,
//       taskType,
//       stationTypeId,
//       completeStatus,
//       startDate,
//       endDate,
//       eventTypeId,
//       userId,
//       keywords,
//       myCompleteStatus,
//       isPush
//     }
//   })
// }

export function getLawListData(pageNum,
  pageSize, taskType,
  stationTypeId,
  completeStatus,
  startDate,
  endDate,
  eventTypeId,
  userId,
  keywords,
  myCompleteStatus,
  isPush) {
  return request({
    url: '/task/linkageTaskPage',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      taskType,
      stationTypeId,
      completeStatus,
      startDate,
      endDate,
      eventTypeId,
      userId,
      keywords,
      myCompleteStatus,
      isPush
    }
  })
}
// 获取当前执法事件详情
export function getLawEventDetail(taskId, userId) {
  return request({
    url: '/task/getTaskDetail',
    method: 'get',
    params: {
      taskId,
      userId
    }
  })
}

// 提交任务描述
export function completeEmergency(data) {
  return request({
    url: '/task/joint_law_enforcement_task/remark',
    method: 'post',
    data
  })
}
// 获取预案列表
export function getPlanListData(accountId, type) {
  return request({
    url: '/task/joint_law_linkage_plan',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params: {
      accountId,
      type
    }
  })
}
/**
 * @method functionName
 * @{taskType} 任务类型
 * @{eventTypeId} 事件类型
 * @{completeStatus} 完成状态
 * @{userId} 用户id
 * @{startDate} 开始日期
 * @{endDate} 结束日期
 * @description 导出
 */
export function exportExcel(taskType,
  stationTypeId,
  eventTypeId,
  completeStatus,
  userId,
  startDate,
  endDate,
  keywords,
  myCompleteStatus) {
  return request({
    url: '/task/export',
    method: 'get',
    responseType: 'blob',
    timeout: 1000 * 60,
    params: {
      taskType: 1,
      stationTypeId,
      eventTypeId,
      completeStatus,
      userId,
      startDate,
      endDate,
      keywords,
      myCompleteStatus
    }
  })
}
// 新增任务 分组: 任务管理 负责人: 杨郁沛
export function addTask(data) {
  return request({
    url: `/task/createTask`,
    method: 'post',
    timeout: 300000,
    data
  })
}

// 获取历史数据
export function getHistoricalData(pageNum, pageSize, taskId) {
  return request({
    url: '/task/getTaskAlarmPage',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      taskId
    }
  })
}
// 上报任务
export function taskPush(params) {
  return request({
    url: `/task/taskPush`,
    method: 'get',
    timeout: 30000,
    params
  })
}
