import request from '@/utils/request'

// export function login(username, password, messageCode, openId, ValidateCode) {
//   return request({
//     url: `/login/handler?account=${username}&password=${password}&code=${messageCode}&openId=${openId}&captcha=${ValidateCode}`,
//     method: 'post'
//   })
// }

export function login(formData) {
  return request({
    url: '/auth/login/handler',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

export function getValidateCode(nowTime) {
  return request({
    url: '/auth/code/image',
    method: 'get',
    params: {
      times: nowTime
    }
  })
}

export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

