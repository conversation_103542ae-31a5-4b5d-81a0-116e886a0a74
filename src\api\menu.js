import request from '@/utils/request'

export function fetchMenuList(departmentIdList) {
  return request({
    url: '/api/vi/menu/menuList',
    method: 'post',
    data: {
      ids: departmentIdList
    }
  })
}

export function _addMenu(menuName, parentId, sort) {
  return request({
    url: '/api/vi/menu/insertMenu',
    method: 'post',
    data: {
      menuName,
      parentId,
      sort
    }
  })
}

export function _delMenu(id) {
  return request({
    url: '/api/vi/menu/deleteMenu',
    method: 'post',
    data: {
      id
    }
  })
}

export function _updateMenu(id, menuName) {
  return request({
    url: '/api/vi/menu/updateMenu',
    method: 'post',
    data: {
      id,
      menuName
    }
  })
}
