import request from '@/utils/request'
import qs from 'qs'
// 新增通知
export function addNotice(data) {
  return request({
    url: '/task/notice/createNotice',
    method: 'post',
    timeout: 60000,
    data
  })
}
// 通知分页列表
export function noticeList(pageNum, pageSize, isPublisher, userId, startDate, endDate, keywords) {
  return request({
    url: '/task/notice/getNoticePage',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      isPublisher,
      userId,
      startDate,
      endDate,
      keywords
    }
  })
}
export function noticeMyList(pageSize, pageNum, initiatingDepartmentId, accountId, createTime, updateTime) {
  return request({
    url: '/task/notice/list',
    method: 'post',
    data: {
      pageSize,
      pageNum,
      initiatingDepartmentId,
      accountId,
      createTime,
      updateTime
    }
  })
}
// 通知详情
export function noticeDetails(noticeId, userId, arg3, arg4) {
  return request({
    url: `/task/notice/getNoticeDetail`,
    method: 'get',
    params: {
      noticeId,
      userId
    }
  })
}
// 通知回复
export function noticeReply(noticeId, comment, departmentId, userId, replyToDepartmentId, replyToUserId, parentId) {
  return request({
    url: '/task/notice/comment',
    method: 'post',
    data: {
      noticeId,
      comment,
      departmentId,
      userId,
      replyToDepartmentId,
      replyToUserId,
      parentId
    }
  })
}
// 根据通知消息id查询回复列表
export function replyLists(noticeId, arg2, arg3, arg4) {
  return request({
    url: `/task/notice/getNoticeComment`,
    method: 'get',
    params: {
      noticeId
    }
  })
}
// 新增子回复
export function noticeReplyChild(replyId, repliedPersonId, accountId, content, noticeId) {
  return request({
    url: '/task/notice/reply/add_child_reply',
    method: 'post',
    data: {
      replyId,
      repliedPersonId,
      accountId,
      content,
      noticeId
    }
  })
}
// 删除回复
export function delReply(replyId, noticeId, accountId, mid, repliedPersonId) {
  return request({
    url: '/task/notice/reply/remove_reply',
    method: 'post',
    data: {
      replyId,
      noticeId,
      accountId,
      mid,
      repliedPersonId
    }
  })
}
// 日志记录
export function noticeLog(noticeId, arg2, arg3, arg4) {
  return request({
    url: `/task/notice/log/${noticeId}`,
    method: 'get'
  })
}
// 执行部门选择列表
export function departmentList(departmentId, deptId) {
  return request({
    url: `/department/optionList`,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify({
      departmentId,
      deptId
    })
  })
}
// 部门层级展示列表
export function departmentIdList(departmentId) {
  return request({
    url: '/department/level_list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify({
      departmentId
    })
  })
}
// 删除列表
export function deleteReservePlan(noticeId) {
  return request({
    url: '/task/notice/delete',
    method: 'post',
    data: {
      noticeId
    }
  })
}
// 获取未读消息数量
export function getMessageNum(params) {
  return request({
    url: '/applets/notice/unread',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 消息列表
export function getMessageList(data) {
  return request({
    url: '/task/message/webMessageList',
    method: 'post',
    data
  })
}
// 已读单个消息
export function getMessageRead(params) {
  return request({
    url: '/task/message/oneRead',
    method: 'get',
    params
  })
}

/**
 * 一键已读
 * @param {*用户id} userId
 * @returns
 */
export function readMessage(userId) {
  return request({
    url: `/task/message/read?userId=${userId}`,
    method: 'get'
  })
}
