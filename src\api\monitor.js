import request from '@/utils/request'
// 获取水站点质列表
export function getWaterStationList() {
  return request({
    url: '/water/getAllStation',
    method: 'get'
  })
}
// 根据水质站点获取排口站点
export function getlistByWaterStation(stationId) {
  return request({
    url: `/water/drain-station/listByWaterStation/${stationId}`,
    method: 'get'
  })
}
// 获取分页列表
export function getList(data) {
  return request({
    url: '/water/drain-station/cameraList',
    method: 'post',
    data
  })
}
// 获取直播地址
export function getLiveAddress(channelId) {
  return request({
    url: `/water/water-camera/live/${channelId}`,
    method: 'get'
  })
}
// 获取排口站点
export function getDrainStation() {
  return request({
    url: '/water/drain-station/list',
    method: 'get'
  })
}
// 获取排口站点监控列表
export function getListNotBindCamera() {
  return request({
    url: '/water/drain-station/listNotBindCamera',
    method: 'get'
  })
}
// 获取排口信息id
export function getListNotBindDrainCamera() {
  return request({
    url: '/water/drain-station/listNotBindDrainCamera',
    method: 'get'
  })
}
// 新增排口
export function saveDrainStation(data) {
  return request({
    url: '/water/drain-station/save',
    method: 'post',
    data
  })
}
// 修改排口
export function updateDrainStation(data) {
  return request({
    url: '/water/drain-station/update',
    method: 'put',
    data
  })
}
// 获取排口站点详情
export function getDrainStationDetail(stationId) {
  return request({
    url: `/water/drain-station/getById/${stationId}`,
    method: 'get'
  })
}
// 获取天气
export function getWether(stationId) {
  return request({
    url: '/weather/getLastWater',
    method: 'get'
  })
}
// 获取监测信息
export function getByDrain(drainId) {
  return request({
    url: `/water/water-drain-sampler-monitor-record/getByDrain/${drainId}`,
    method: 'get'
  })
}
// 获取监测详情
export function getMonitorDetail(data) {
  return request({
    url: '/water/drain-station/getDetails',
    method: 'get',
    params: data
  })
}
