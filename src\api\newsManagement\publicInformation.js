import request from '@/utils/request'
// ----------信息管理接口------------

/**
 * @description 信息列表
 * @needData data-分页查询数据
 * <AUTHOR>
 */
export function getPage(data) {
  return request({
    url: `/waterSource/message/page`,
    method: 'post',
    data
  })
}
/**
 * @description 新增信息
 * @needData data-信息数据
 * <AUTHOR>
 */
export function doSave(data) {
  return request({
    url: `/waterSource/message/save`,
    method: 'post',
    data
  })
}
/**
 * @description 修改信息
 * @needData data-信息数据
 * <AUTHOR>
 */
export function doUpdate(data) {
  return request({
    url: `/waterSource/message/update`,
    method: 'put',
    data
  })
}
/**
 * @description 删除信息
 * @needData id-信息id
 * <AUTHOR>
 */
export function dodDlete(id) {
  return request({
    url: `/waterSource/message/delete?id=${id}`,
    method: 'delete'
  })
}
/**
 * @description 公示状态修改
 * @needData data-修改的数据
 * <AUTHOR>
 */
export function updateShow(data) {
  return request({
    url: `/waterSource/message/updateShow`,
    method: 'put',
    data
  })
}
/**
 * @description 数据权限列表(用作发稿部门筛选)
 * @needData 无
 * <AUTHOR>
 */
export function dataPermissionSelection() {
  return request({
    url: `/system/department/dataPermissionSelection`,
    method: 'get'
  })
}
/**
 * @description 数据权限列表(用作发稿部门筛选)
 * @needData 无
 * <AUTHOR>
 */
export function addView(id) {
  return request({
    url: `/waterSource/message/addView?id=${id}`,
    method: 'get'
  })
}
