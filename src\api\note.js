import request from '@/utils/request'

/**
 * @description获取短信配置列表
 */
// eslint-disable-next-line import/prefer-default-export
export function getNoteList(data) {
  return request({
    url: '/system/smsStation/getSmsStationPage',
    method: 'post',
    data
  })
}
/**
 * @description 根据站点类型id获取站点名字
 */
export function getStationList(typeId) {
  return request({
    url: '/system/smsStation/getStationName',
    method: 'get',
    params: {
      typeId
    }
  })
}
/**
 * @params {accountId}当前登录账户id
 * @description 根据accountId返回树状结构账户数据
 */
export function getListTreeByDept(accountId) {
  return request({
    url: `/system/account/listTreeByDept/${accountId}`,
    method: 'get'
  })
}
/**
 * @description 更新站点接收者数据
 * @params{smsId}短信配置id
 * @params{recipientIds}短信接收者id
 */
export function updateRecipientRel(data) {
  return request({
    url: '/system/smsStation/updateRecipientRel',
    method: 'post',
    data
  })
}
/**
 * @description获取短信配置详情
 * @params{id}配置项id
 */
export function getConfigDetail(id = 0) {
  return request({
    url: `/system/smsPlatformConfig/getConfigDetail/${id}`,
    method: 'get'
  })
}
/**
 * @description获取模板配置详情
 * @params{id}配置项id
 */
export function getTemplateDetail(templateId = 0) {
  return request({
    url: `/system/smsPlatformConfig/getTemplateDetail/${templateId}`,
    method: 'get'
  })
}
/**
 * @params{id}模板id
 * @params{templateName}模板配置
 * @params{smsSign}模板签名
 * @params{templateCode}模板代码
 * @params{templateContent}模板内容
 * @description更新短信模板配置
 */
export function updateTemplate(id, smsSign, templateCode) {
  return request({
    url: `/system/smsPlatformConfig/updateTemplate`,
    method: 'post',
    data: {
      id, smsSign, templateCode
    }
  })
}
/**
 * @params{id} 短信平台id
 * @params{publicKey}公钥
 * @params{privateKey}私钥
 */
export function updatePlatformConfig(id, publicKey, privateKey) {
  return request({
    url: `/system/smsPlatformConfig/updatePlatformConfig`,
    method: 'post',
    data: {
      id, publicKey, privateKey
    }
  })
}
/**
 * @params {pageNum} 分页页码
 * @params {pageSize} 每页条数
 * @params {keywords} 关键词
 * @params {from} 开始时间
 * @params {to} 截止时间
 * @params {sendStatus} 发送状态
 */
export function getLogList(data) {
  return request({
    url: `/system/smsLog/getSmsAlarmLogPage`,
    method: 'post',
    data
  })
}
/**
 * @params {keywords} 关键字
 * @method 按排口获取列表
 */
export function getRiverlist(keywords, userId) {
  return request({
    url: `/web/water/drain/user/getDrainUserList`,
    method: 'get',
    params: {
      keywords,
      userId
    }
  })
}
/**
 * @params {drainId} 排口id
 * @params {userId} 用户id
 * @method 删除绑定人员
 */
export function removePerson(data) {
  return request({
    url: `/web/water/drain/user/delete`,
    method: 'post',
    data
  })
}
/**
 * @params {keywords} 关键字
 * @method 按排口获取人员列表
 */
export function getUserlist(keywords, drainId) {
  return request({
    url: `/web/water/drain/user/getUserDrainList`,
    method: 'get',
    params: {
      keywords,
      drainId
    }
  })
}
/**
 * @params {drainId} 排口id
 * @params {userId} 人员id
 * @params {userIds} 人员id列表
 * @params {drainIds} 排口id列表
 * @method 更新排口人员
 */
export function updateDrain(data) {
  return request({
    url: `/web/water/drain/user/update`,
    method: 'post',
    data
  })
}
