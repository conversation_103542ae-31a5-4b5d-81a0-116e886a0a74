/* eslint-disable */
import request from '@/utils/request'
import qs from 'qs'
// 新建巡查任务
export function addPatrol(patrolTitle, patrolContent, patrolType, executePerson, placeList, annexFiles, creator, frequency, timeInterval, startDate, endDate) {
  return request({
     url: '/task/patrol/add',
    method: 'post',
    timeout: 60000,
    data: {
      patrolTitle,
      patrolContent,
      patrolType,
      executePerson,
      placeList,
      annexFiles,
      creator,
      frequency,
      timeInterval,
      startDate,
      endDate
    }
  })
}
// 分页查询巡查任务
export function patrolList(pageNum, pageSize, creator, type, status, executePerson, startDate, endDate) {
  return request({
     url: '/task/patrol/page-list',
    method: 'post',
    data: {
      pageNum,
      pageSize,
      status,
      executePerson,
      startDate,
      endDate,
      type,
      creator
    }
  })
}
// 删除巡查任务
export function deletePatrol(patrolId) {
  return request({
    url: `/task/patrol/delete/${patrolId}`,
    method: 'delete'
  })
}
// 根据巡查任务id 查询详情
export function patrolIdList(patrolId) {
  return request({
    url: `/task/patrol/details/${patrolId}`,
    method: 'get'
  })
}
// 开始巡查
export function startPatrol(patrolId, userId, userName) {
  return request({
     url: '/task/patrol-record/start-patrol',
    method: 'post',
    data: {
      patrolId,
      userId,
      userName
    }
  })
}
// 到点打卡
export function recordPatrol(placeId, longitude, latitude, patrolRecordId) {
  return request({
     url: '/task/patrol-record/record-place',
    method: 'post',
    data: {
      placeId,
      longitude,
      latitude,
      patrolRecordId
    }
  })
}
// 完成巡查提交
export function completePatrol(id, remark, files) {
  return request({
     url: '/task/patrol-record/complete',
    method: 'post',
    data: {
      id,
      remark,
      files
    }
  })
}
// 执行部门选择列表
export function departmentList(departmentId, deptId) {
  return request({
    url: `/system/department/optionList`,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify({
      departmentId,
      deptId
    })
  })
}
// 修改执行者
export function executePerson(patrolId, executePerson, executePersonName) {
  return request({
     url: '/task/patrol/modify-executePerson',
    method: 'post',
    data: {
      patrolId,
      executePerson,
      executePersonName
    }
  })
}
// 部门层级展示列表
export function departmentIdList(departmentId) {
  return request({
     url: '/aystem/department/level_list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify({
      departmentId
    })
  })
}

// 获取巡岗列表
// export function getPartolListData(data) {
//   return request({
//      url: '/joint_law_enforcement_task/check_task/list',
//     method: 'post',
//     data: data
//   })
// }
export function getPartolListData(params) {
  return request({
     url: '/task/getTaskPage',
    method: 'get',
    params
  })
}
// 删除单条巡查信息
export function DeletePatrol(id) {
  return request({
    url: `/task/joint_law_enforcement_task/report/${id}`,
    method: 'delete'
  })
}
// 获取巡查信息详情
// export function getTaskDetail(id) {
//   return request({
//      url: '/joint_law_enforcement_task/check_task/details',
//     method: 'get',
//     params: {
//       taskId: id
//     }
//   })
// }
export function getTaskDetail(taskId) {
  return request({
    url: `/task/joint_law_enforcement_task/check_task/taskDetail?taskId=${taskId}`,
    method: 'get'
  })
}
// 获取任务详情
export function getTaskDetailTwo(taskId, userId) {
  return request({
    url: `/task/getTaskDetail?taskId=${taskId}&userId=${userId}`,
    method: 'get'
  })
}
// 审核巡查任务
export function checkTask(data) {
  return request({
     url: '/task/joint_law_enforcement_task/check_task',
    method: 'put',
    data
  })
}
// 删除巡查任务
export function deleteReservePlan(id) {
  return request({
    url: `/task/patrol/delete/${id}`,
    method: 'delete'
  })
}
// 巡岗上报管理端excel导出
export function exportExcelAll(data) {
  return request({
     url: '/task/joint_law_enforcement_task/check_task/exportTaskInfo',
    method: 'post',
    responseType: 'arraybuffer',
    data
  })
}
// 巡岗上报修改为忽略接口(NEW)
export function ignore(taskId) {
  return request({
    url: `/task/joint_law_enforcement_task/check_task/updateOverlook/${taskId}`,
    method: 'post'
  })
}

/**
 * 巡岗上报审核
 * @param {*} data
 * @returns
 */
export function patrolReportAudit(data) {
  return request({
     url: '/task/joint_law_enforcement_task/check_task/audit',
    method: 'post',
    data
  })
}

/**
 * 审核拒绝
 */
export function refuseTask(data) {
  return request({
     url: '/task/node/refuse',
    method: 'post',
    data
  })
}
/**
 * 街道部门列表
 */
 export function getStreetList() {
  return request({
     url: '/system/street-department/list',
    method: 'get'
  })
}
/**
 * 站点部门列表
 */
 export function getDepartmentList() {
  return request({
     url: '/system/station-type-department/list',
    method: 'get'
  })
}
 /**
  * 巡岗上报流转
  */
  export function TaskNodeFlow(data) {
    return request({
       url: '/task/node/flow',
      method: 'post',
      data
    })
  }
/**
 * 巡岗上报完成
 */
 export function completedTask(data) {
  return request({
     url: '/task/node/feedback',
    method: 'post',
    data
  })
}


// 污染类型下来（有其他）type 0 正常巡岗 1 异常巡岗
export function pollutionTypeSelect(data) {
  return request({
     url: '/task/web/patrol_reporting/findUploadType',
    method: 'get',
    params:data
  })
}

// 污染类型下来（有其他）
export function pollutionType(data) {
  return request({
     url: '/task/web/patrol_reporting/uploadType',
    method: 'get',
    params:data
  })
}


