/* eslint-disable */
import request from '@/utils/request'
import qs from 'qs'


/**  巡查计划 start  */
// 巡查计划日历表
export function patrolDayList(data) {
  return request({
     url: '/waterSource/inspectionPlan/list',
    method: 'post',
    data
  })
}

// 巡查计划日历表新增
export function eventSave(data) {
  return request({
     url: '/waterSource/inspectionPlan/save',
    method: 'post',
    data
  })
}

// 巡查计划日历表详情
export function eventDetail({id}) {
  return request({
     url: '/waterSource/inspectionPlan/detail?id='+id,
    method: 'get',
  })
}

// 巡查计划日历表详情
export function eventUpdate(data) {
  return request({
     url: '/waterSource/inspectionPlan/update',
    method: 'put',
    data
  })
}

// 巡查计划右键删除
export function eventDelete(data) {
  return request({
     url: '/waterSource/inspectionPlan/delete',
    method: 'DELETE',
    data
  })
}
/**  巡查计划 end */





/** 巡查记录 start  */

// 巡查记录列表
export function patrolList(data) {
  return request({
     url: '/waterSource/inspectionPlan/inspectionPlanPage',
    method: 'post',
    data
  })
}


/**
 * @method functionName
 * @param {title} 标题
 * @param {state} 	状态（0未开始,1进行中，2未完成，3已完成）
 * @param {startTime} 开始时间 yyyy-mm-dd hh:ss
 * @param {EndTime} 结束日期
 */
 export function exportExcel(params) {
  return request({
    url: `/waterSource/inspectionPlan/export`,
    method: 'post',
    responseType: 'blob',
    data:params
  })
}

/**
 * 提交反馈处理结果
 * @param {Object} data {
 * "id": "对应详情里面的punchRecords里对象的id"
 * "feedBack": "反馈内容（限制255个字符）"
 * }
 * @returns
 */
export function submitFeedback({id,feedBack}) {
  if(!id || !feedBack) return Promise.reject('请传入必要参数')
  return request({
     url: '/waterSource/inspectionPlan/feedBack',
    method: 'post',
    data: {
      id,
      feedBack
    }
  })
}

/** 巡查记录 end  */
