import request from '@/utils/request'
/**
 * @description 巡航视频列表
 */
export function machinePage(data) {
  return request({
    url: `/waterSource/underwater/machine/page`,
    method: 'post',
    data
  })
}

/**
 * @description 添加水下巡航视频
 */
export function machineSave(data) {
  return request({
    url: `/waterSource/underwater/machine/save`,
    method: 'post',
    data
  })
}

/**
 * @description 修改水下巡航视频
 */
export function machineUpdate(data) {
  return request({
    url: `/waterSource/underwater/machine/update`,
    method: 'put',
    data
  })
}

/**
 * @description 删除水下巡航视频
 */
export function machineDel(id) {
  return request({
    url: `/waterSource/underwater/machine/del?id=${id}`,
    method: 'delete'
  })
}

