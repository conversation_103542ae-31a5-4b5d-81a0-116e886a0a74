import request from '@/utils/request'
// eslint-disable-next-line import/no-extraneous-dependencies
import qs from 'qs'

// 修改个人用户密码
export function changePassword(account, oldPassword, newPassword) {
  return request({
    url: '/system/account/password_set',
    method: 'put',
    data: {
      account, oldPassword, newPassword
    }
  })
}

// 修改个人用户手机账号
export function changeTelephoneNumber(oldPhoneNumber, newPhoneNumber, code, arg4) {
  return request({
    url: '/system/account',
    method: 'put',
    data: qs.stringify({
      oldPhoneNumber,
      newPhoneNumber,
      code
    }),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 获取修改手机号验证码
export function fetchTelephoneCode(phoneNumber, arg2, arg3, arg4) {
  return request({
    url: '/sms/modify-mobile-code',
    method: 'post',
    data: qs.stringify({ phoneNumber }),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

//
// eslint-disable-next-line max-len
export function updateUserInfor(accountId, accountName, political, education, idCard, avatar, userName) {
  return request({
    url: '/system/user/update',
    method: 'put',
    data: {
      accountId,
      accountName,
      political,
      education,
      idCard,
      baseFile: avatar,
      userName
    }
  })
}

export function functionName1(arg1, arg2, arg3, arg4) {
  return request({
    url: '',
    method: 'post',
    data: {

    }
  })
}

export function functionName2(arg1, arg2, arg3, arg4) {
  return request({
    url: '',
    method: 'get',
    params: {

    }
  })
}
