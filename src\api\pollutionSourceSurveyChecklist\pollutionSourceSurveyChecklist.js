import request from '@/utils/request'

// 区划下拉
export function doDivisionList() {
  return request({
    url: '/system/pollutionSourceSurvey/doDivisionList',
    method: 'get'
  })
}

export function taskTrackDetail(params) {
  return request({
    url: '/air/eps/detail',
    method: 'post',
    params
  })
}

export function taskTrackPageList(params) {
  return request({
    url: '/air/eps/pageList',
    method: 'post',
    params
  })
}
export function getPollutionSourceList(data) {
  return request({
    url: '/system/pollutionSourceSurvey/doPage',
    method: 'post',
    data
  })
}
// 获取行业下拉
export function getIndustryList() {
  return request({
    url: 'system/industry/type/listThree',
    method: 'get'
  })
}
// 施工阶段下拉
export function getConstructionTypeList() {
  return request({
    url: '/system/pollutionSourceSurvey/doConstructionType',
    method: 'get'
  })
}
// 区县下拉
export function getDistrictList() {
  return request({
    url: '/system/pollutionSourceSurvey/doDistrictList',
    method: 'get'
  })
}
// 街道下拉
export function getStreetList(params) {
  return request({
    url: '/system/pollutionSourceSurvey/doStreetList',
    method: 'get',
    params
  })
}
// 工地类型下拉
export function getSiteTypeList() {
  return request({
    url: `/system/dict/doList?type=3`,
    method: 'get'
  })
}
// 主要污染物下拉
export function getMianPollutionList(params) {
  return request({
    url: '/system/pollutionSourceSurvey/pollutionItem',
    method: 'get',
    params
  })
}
// 新增污染源

export function doSave(data) {
  return request({
    url: 'system/pollutionSourceSurvey/doSave',
    method: 'post',
    data
  })
}
// 修改污染源
export function doEdit(data) {
  return request({
    url: 'system/pollutionSourceSurvey/doUpdate',
    method: 'post',
    data
  })
}
// 删除污染源
export function doDel(params) {
  return request({
    url: '/system/pollutionSourceSurvey/doDelete',
    method: 'delete',
    params
  })
}
// 获取监测项列表
export function getPollutionMonitorItem(params) {
  return request({
    url: '/system/pollutionSourceSurvey/pollutionMonitorItem',
    method: 'get',
    params
  })
}
// 获取监测信息
export function getPollutionMonitorData(data) {
  return request({
    url: '/system/pollutionSourceSurvey/pollutionMonitorData',
    method: 'post',
    data
  })
}
// 获取监控分页列表
export function getCameraList(params) {
  return request({
    url: '/system/pollutionSourceSurvey/pollutionCameraList',
    method: 'get',
    params
  })
}
// 导出监测数据
export function exportPollution(params) {
  return request({
    url: '/system/pollutionSourceSurvey/pollutionMonitorData/export',
    'Content-Type': 'multipart/form-data',
    responseType: 'blob',
    params
  })
}
// 导出污染源调查清单
export function exportPollutionCheckList(params) {
  return request({
    url: '/system/pollutionSourceSurvey/export',
    'Content-Type': 'multipart/form-data',
    responseType: 'blob',
    params
  })
}
