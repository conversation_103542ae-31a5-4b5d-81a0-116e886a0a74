import request from '@/utils/request'

export function fetchPostionList() {
  return request({
    url: '/position/positionList',
    method: 'post'
  })
}

export function _addPostion(positionName) {
  return request({
    url: '/position/insertPosition',
    method: 'post',
    data: {
      positionName
    }
  })
}

export function _delPostion(id) {
  return request({
    url: '/position/deletePosition',
    method: 'post',
    data: {
      id
    }
  })
}

export function _updatePostion(id, positionName) {
  return request({
    url: '/position/updatePosition',
    method: 'post',
    data: {
      id,
      positionName
    }
  })
}
