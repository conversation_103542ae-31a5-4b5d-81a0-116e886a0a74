import request from '@/utils/request'

// 分页获取流程机器人列表
// eslint-disable-next-line import/prefer-default-export
export function pageList(params) {
  return request({
    url: '/task/task-smart-push/pageList',
    method: 'get',
    params
  })
}
// 新增流程机器人
export function save(data) {
  return request({
    url: '/task/task-smart-push/save',
    method: 'post',
    data
  })
}
// 修改流程机器人
export function update(data) {
  return request({
    url: '/task/task-smart-push/update',
    method: 'put',
    data
  })
}
// 启用禁用 流程机器人
export function enablePush(data) {
  return request({
    url: '/task/task-smart-push/enablePush',
    method: 'put',
    data
  })
}
// 删除 流程机器人
export function deleteById(id) {
  return request({
    url: `/task/task-smart-push/delete?id=${id}`,
    method: 'delete'
  })
}
