import request from '@/utils/request'

export function reportList(pageNum, pageSize, userId, eventTypeId, approveStatus, startDate, endDate, keywords) {
  return request({
    url: '/system/consumptionReport/getReportPage',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      userId,
      eventTypeId,
      approveStatus,
      startDate,
      endDate,
      keywords
    }
  })
}
export function reportCheckList(pageNum, pageSize, departmentId, eventTypeId, approveStatus, startDate, endDate, keywords) {
  return request({
    url: '/system/consumptionReport/getApproveReportPage',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      departmentId,
      eventTypeId,
      approveStatus,
      startDate,
      endDate,
      keywords
    }
  })
}
export function reportDetails(reportId, arg2, arg3, arg4) {
  return request({
    url: `/system/consumption_report/${reportId}`,
    method: 'get'
  })
}

export function reportCheck(data) {
  return request({
    url: `/system/consumptionReport/approve`,
    method: 'post',
    data
  })
}
export function getReportDetail(taskId) {
  return request({
    url: `/system/consumptionReport/getDetailPage`,
    method: 'get',
    params: {
      reportId: taskId
    }
  })
}
