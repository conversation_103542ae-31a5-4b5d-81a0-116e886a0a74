import request from '@/utils/request'

/*
* 行为分析列表
*/
export function getbehaviorList(data) {
  return request({
    url: '/waterSource/behavior-analysis/page',
    method: 'post',
    data
  })
}

/*
*污染日历
*/
export function calendar(date) {
  return request({
    url: `/waterSource/behavior-analysis/calendar?date=${date}`,
    method: 'get'
  })
}

/*
 *报警趋势(近6个月)
*/
export function trend() {
  return request({
    url: '/waterSource/behavior-analysis/trend',
    method: 'get'
  })
}
/*
 *报警统计(本年)
*/
export function statistical() {
  return request({
    url: '/waterSource/behavior-analysis/statistical',
    method: 'get'
  })
}

// /* 行为分析-抓拍记录 */
// export function getbehaviorList(data) {
//   return request({
//      url: '/web/behavior-analysis/records',
//     method: 'post',
//     data
//   })
// }

// /*设置详情*/
// export function getdetail(cameraSerial) {
//   return request({
//     url: `/web/behavior-analysis/detail`,
//     method: 'get'
//   })
// }

// /* 行为分析设置--新增or修改 */
// export function getsaveOrUpdate(data) {
//   return request({
//      url: '/web/behavior-analysis/saveOrUpdate',
//     method: 'post',
//     data
//   })
// }

