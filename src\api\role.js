import request from '@/utils/request'

export function fetchUserList(pageNum, pageSize, departmentId, position, name, groupId) {
  return request({
    url: '/user/userList',
    method: 'post',
    data: {
      pageNum,
      pageSize,
      params: {
        departmentId,
        position,
        name,
        groupId
      }
    }
  })
}

export function fetchDepartmentList(id) {
  return request({
    url: '/user/departmentList',
    method: 'post',
    data: {
      id
    }
  })
}

export function addRole(name, positionId, telephone, password, departmentId, groupId, departmentList) {
  return request({
    url: '/user/add',
    method: 'post',
    data: {
      name,
      positionId,
      telephone,
      // password: password,
      departmentId,
      groupId,
      departmentList
    }
  })
}

export function delRole(id) {
  return request({
    url: '/user/deleteUser',
    method: 'post',
    data: {
      id
    }
  })
}

export function updateRole(id, name, positionId, telephone, departmentId, groupId, departmentList) {
  return request({
    url: '/user/updateUser',
    method: 'post',
    data: {
      id,
      name,
      positionId,
      telephone,
      departmentId,
      groupId,
      departmentList
    }
  })
}

// export function getRoutes() {
//   return request({
//      url: '/routes',
//     method: 'get'
//   })
// }

// export function getRoles() {
//   return request({
//      url: '/roles',
//     method: 'get'
//   })
// }

// export function addRole(data) {
//   return request({
//      url: '/role',
//     method: 'post',
//     data
//   })
// }

// export function updateRole(id, data) {
//   return request({
//     url: `/role/${id}`,
//     method: 'put',
//     data
//   })
// }

// export function deleteRole(id) {
//   return request({
//     url: `/role/${id}`,
//     method: 'delete'
//   })
// }
