/* eslint-disable */
import request from '@/utils/request'
import qs from 'qs'
// 报表列表
// export function reportList() {
//   return request({
//      url: '/classification/report/reportList',
//     method: 'get'
//   })
// }
export function reportList(accountId) {
  return request({
    url: `/refuse/classification/report/reportListByAccountId/${accountId}`,
    method: 'get'
  })
}
// 执行部门选择列表
export function departmentList(accountId) {
  return request({
    url: `/system/department/refuseGetDepartmentAccount?accountId=${accountId}`,
    method: 'get'
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded'
    // }
  })
}
// 获取局内执行者列表 分组: 任务管理 负责人: 杨郁沛
export function getPersonList(departmentId, withStreet) {
  return request({
    url: `/system/department/getTaskExecutorList`,
    method: 'get',
    params: {
      departmentId,
      withStreet
    }
  })
}
// 添加分类报表人员绑定
export function addReportUser(reportUser) {
  return request({
     url: '/refuse/report/join/user/insert',
    method: 'post',
    data: reportUser
  })
}
// 删除人员和报表绑定
export function deleteReportUser(departmentId, reportId) {
  return request({
     url: '/refuse/report/join/user/delete',
    method: 'delete',
    data: {
      departmentId,
      reportId
    }
  })
}
// 人员列表
export function reportUserList(reportId) {
  return request({
     url: '/refuse/report/join/user/userList',
    method: 'post',
    data: {
      reportId
    }
  })
}

// -----------分类收集--------------------------------------
// 保存分类收集
export function saveClassifiedCollection(accountId, list) {
  return request({
     url: '/refuse/classified_collection',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 分类收集详情
export function getClassifiedCollection(accountId) {
  return request({
    url: `/refuse/classified_collection/${accountId}`,
    method: 'get'
  })
}
// 分类收集分页
export function listClassifiedCollection(accountId, pageNum, pageSize, startTime, endTime) {
  return request({
     url: '/refuse/classified_collection/list',
    method: 'post',
    data: {
      accountId,
      startTime,
      endTime,
      pageNum,
      pageSize
    }
  })
}
// 分类收集分页详情
export function detailsClassifiedCollection(accountId, year, month) {
  return request({
     url: '/refuse/classified_collection/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      month
    }
  })
}
// 导出分类收集
export function exportClassifiedCollection(accountId, year, month) {
  return request({
     url: '/refuse/classified_collection/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      month
    }
  })
}

// --------------------------------------分类运输--------------------------------------
// 保存分类运输
export function saveClassifiedTransport(accountId, list) {
  return request({
     url: '/refuse/classified_transport',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 分类运输详情
export function getClassifiedTransport(accountId) {
  return request({
    url: `/refuse/classified_transport/${accountId}`,
    method: 'get'
  })
}
// 分类运输分页
export function listClassifiedTransport(accountId, pageNum, pageSize, startTime, endTime) {
  return request({
     url: '/refuse/classified_transport/list',
    method: 'post',
    data: {
      accountId,
      startTime,
      endTime,
      pageNum,
      pageSize
    }
  })
}
// 分类运输分页详情
export function detailsClassifiedTransport(accountId, year, month) {
  return request({
     url: '/refuse/classified_transport/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      month
    }
  })
}
// 导出分类运输
export function exportClassifiedTransport(accountId, year, month) {
  return request({
     url: '/refuse/classified_transport/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      month
    }
  })
}

// --------------------------------------生活垃圾分类覆盖情况--------------------------------------
// 保存生活垃圾分类覆盖情况
export function saveClassifiedCover(accountId, list) {
  return request({
     url: '/refuse/classified_cover',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 生活垃圾分类覆盖情况详情
export function getClassifiedCover(accountId) {
  return request({
    url: `/refuse/classified_cover/${accountId}`,
    method: 'get'
  })
}
// 生活垃圾分类覆盖情况分页
export function listClassifiedCover(accountId, pageNum, pageSize, startTime, endTime) {
  return request({
     url: '/refuse/classified_cover/list',
    method: 'post',
    data: {
      accountId,
      startTime,
      endTime,
      pageNum,
      pageSize
    }
  })
}
// 生活垃圾分类覆盖情况分页详情
export function detailsClassifiedCover(accountId, year, month) {
  return request({
     url: '/refuse/classified_cover/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      month
    }
  })
}
// 导出生活垃圾分类覆盖情况
export function exportClassifiedCover(accountId, year, month) {
  return request({
     url: '/refuse/classified_cover/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      month
    }
  })
}

// --------------------------------------示范创建--------------------------------------
// 保存示范创建
export function saveDemonstrationAgency(accountId, list) {
  return request({
     url: '/refuse/demonstration_agency',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 示范创建详情
export function getDemonstrationAgency(accountId) {
  return request({
    url: `/refuse/demonstration_agency/${accountId}`,
    method: 'get'
  })
}
// 示范创建分页
export function listDemonstrationAgency(accountId, pageNum, pageSize, startTime, endTime) {
  return request({
     url: '/refuse/demonstration_agency/list',
    method: 'post',
    data: {
      accountId,
      startTime,
      endTime,
      pageNum,
      pageSize
    }
  })
}
// 示范创建分页详情
export function detailsDemonstrationAgency(accountId, year, month) {
  return request({
     url: '/refuse/demonstration_agency/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      month
    }
  })
}
// 导出示范创建
export function exportDemonstrationAgency(accountId, year, month) {
  return request({
     url: '/refuse/demonstration_agency/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      month
    }
  })
}

// --------------------------------------志愿者活动--------------------------------------
// 保存志愿者活动
export function saveVolunteerActivity(accountId, list, list1) {
  return request({
     url: '/refuse/volunteer_activity',
    method: 'post',
    data: {
      accountId,
      volunteerLists: list,
      volunteerActivities: list1
    }
  })
}
// 志愿者活动详情
export function getVolunteerActivity(accountId) {
  return request({
    url: `/refuse/volunteer_activity/${accountId}`,
    method: 'get'
  })
}
// 志愿者活动情况分页
export function listVolunteerActivity(accountId, pageNum, pageSize, startTime, endTime) {
  return request({
     url: '/refuse/volunteer_activity/list',
    method: 'post',
    data: {
      accountId,
      startTime,
      endTime,
      pageNum,
      pageSize
    }
  })
}
// 志愿者活动分页详情
export function detailsVolunteerActivity(accountId, year, month) {
  return request({
     url: '/refuse/volunteer_activity/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      month
    }
  })
}
// 导出志愿者活动
export function exportVolunteerActivity(accountId, year, month) {
  return request({
     url: '/refuse/volunteer_activity/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      month
    }
  })
}

// --------------------------------------主题宣传活动活动--------------------------------------
// 保存主题宣传活动活动
export function saveThemePromotion(accountId, list) {
  return request({
     url: '/refuse/theme_promotion',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 主题宣传活动详情
export function getThemePromotion(accountId) {
  return request({
    url: `/refuse/theme_promotion/${accountId}`,
    method: 'get'
  })
}
// 主题宣传活动分页
export function listThemePromotion(accountId, pageNum, pageSize, startTime, endTime) {
  return request({
     url: '/refuse/theme_promotion/list',
    method: 'post',
    data: {
      accountId,
      startTime,
      endTime,
      pageNum,
      pageSize
    }
  })
}
// 主题宣传活动分页详情
export function detailsThemePromotion(accountId, year, month) {
  return request({
     url: '/refuse/theme_promotion/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      month
    }
  })
}
// 导出主题宣传活动
export function exportThemePromotion(accountId, year, month) {
  return request({
     url: '/refuse/theme_promotion/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      month
    }
  })
}

// --------------------------------------生活垃圾分类工作成效表--------------------------------------
// 保存生活垃圾分类工作成效表
export function saveDomesticWasteEffect(data) {
  return request({
     url: '/refuse/domestic_waste_effect',
    method: 'post',
    data
  })
}
// 生活垃圾分类工作成效表详情
export function getDomesticWasteEffect(accountId) {
  return request({
    url: `/refuse/domestic_waste_effect/${accountId}`,
    method: 'get'
  })
}
// 生活垃圾分类工作成效表分页
export function listDomesticWasteEffect(accountId, pageNum, pageSize, startTime, endTime) {
  return request({
     url: '/refuse/domestic_waste_effect/list',
    method: 'post',
    data: {
      accountId,
      startTime,
      endTime,
      pageNum,
      pageSize
    }
  })
}
// 生活垃圾分类工作成效表分页详情
export function detailsDomesticWasteEffect(accountId, year, month) {
  return request({
     url: '/refuse/domestic_waste_effect/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      month
    }
  })
}
// 导出生活垃圾分类工作成效表
export function exportDomesticWasteEffect(accountId, year, month) {
  return request({
     url: '/refuse/domestic_waste_effect/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      month
    }
  })
}

// --------------------------------------示范片区作业台账--------------------------------------
// 保存示范片区作业台账
export function saveDemonstrationArea(accountId, list) {
  return request({
     url: '/refuse/demonstration_area',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 示范片区作业台账详情
export function getDemonstrationArea(accountId) {
  return request({
    url: `/demonstration_area/${accountId}`,
    method: 'get'
  })
}
// 示范片区作业台账活动分页
export function listDemonstrationArea(accountId, pageNum, pageSize, startTime, endTime) {
  return request({
     url: '/refuse/demonstration_area/list',
    method: 'post',
    data: {
      accountId,
      startTime,
      endTime,
      pageNum,
      pageSize
    }
  })
}
// 示范片区作业台账分页详情
export function detailsDemonstrationArea(accountId, year, month) {
  return request({
     url: '/refuse/demonstration_area/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      month
    }
  })
}
// 导出示范片区作业台账
export function exportDemonstrationArea(accountId, year, month) {
  return request({
     url: '/refuse/demonstration_area/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      month
    }
  })
}

// --------------------------------------要素投入--------------------------------------
// 保存要素投入
export function saveFactorInput(data) {
  return request({
     url: '/refuse/factor_input',
    method: 'post',
    data
  })
}
// 要素投入详情
export function getFactorInput(accountId) {
  return request({
    url: `/refuse/factor_input/${accountId}`,
    method: 'get'
  })
}
// 要素投入分页
export function listFactorInput(accountId, pageNum, pageSize, year, quarter) {
  return request({
     url: '/refuse/factor_input/list',
    method: 'post',
    data: {
      accountId,
      year,
      quarter,
      pageNum,
      pageSize
    }
  })
}
// 要素投入分页详情
export function detailsFactorInput(accountId, year, quarter) {
  return request({
     url: '/refuse/factor_input/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      quarter
    }
  })
}
// 导出要素投入
export function exportFactorInput(accountId, year, quarter) {
  return request({
     url: '/refuse/factor_input/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      quarter
    }
  })
}

// --------------------------------------收集设施--------------------------------------
// 保存收集设施
export function saveCollectionFacility(accountId, list) {
  return request({
     url: '/refuse/collection_facility',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 收集设施详情
export function getCollectionFacility(accountId) {
  return request({
    url: `/refuse/collection_facility/${accountId}`,
    method: 'get'
  })
}
// 收集设施分页
export function listCollectionFacility(accountId, pageNum, pageSize, year, quarter) {
  return request({
     url: '/refuse/collection_facility/list',
    method: 'post',
    data: {
      accountId,
      year,
      quarter,
      pageNum,
      pageSize
    }
  })
}
// 收集设施分页详情
export function detailsCollectionFacility(accountId, year, quarter) {
  return request({
     url: '/refuse/collection_facility/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      quarter
    }
  })
}
// 导出收集设施
export function exportCollectionFacility(accountId, year, quarter) {
  return request({
     url: '/refuse/collection_facility/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      quarter
    }
  })
}

// --------------------------------------处理利用--------------------------------------
// 保存处理利用
export function saveProcessingUtilization(accountId, list) {
  return request({
     url: '/refuse/processing_utilization',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 处理利用详情
export function getProcessingUtilization(accountId) {
  return request({
    url: `/refuse/processing_utilization/${accountId}`,
    method: 'get'
  })
}
// 处理利用分页
export function listProcessingUtilization(accountId, pageNum, pageSize, year, quarter) {
  return request({
     url: '/refuse/processing_utilization/list',
    method: 'post',
    data: {
      accountId,
      year,
      quarter,
      pageNum,
      pageSize
    }
  })
}
// 处理利用分页详情
export function detailsProcessingUtilization(accountId, year, quarter) {
  return request({
     url: '/refuse/processing_utilization/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      quarter
    }
  })
}
// 导出处理利用
export function exportProcessingUtilization(accountId, year, quarter) {
  return request({
     url: '/refuse/processing_utilization/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      quarter
    }
  })
}

// --------------------------------------生活垃圾分类收运车辆--------------------------------------
// 保存生活垃圾分类收运车辆
export function saveSortingTruck(accountId, list) {
  return request({
     url: '/refuse/sorting_truck',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 生活垃圾分类收运车辆详情
export function getSortingTruck(accountId) {
  return request({
    url: `/refuse/sorting_truck/${accountId}`,
    method: 'get'
  })
}
// 生活垃圾分类收运车辆分页
export function listSortingTruck(accountId, pageNum, pageSize, year, quarter) {
  return request({
     url: '/refuse/sorting_truck/list',
    method: 'post',
    data: {
      accountId,
      year,
      quarter,
      pageNum,
      pageSize
    }
  })
}
// 生活垃圾分类收运车辆分页详情
export function detailsSortingTruck(accountId, year, quarter) {
  return request({
     url: '/refuse/sorting_truck/record_details',
    method: 'post',
    data: {
      accountId,
      year,
      quarter
    }
  })
}
// 导出生活垃圾分类收运车辆
export function exportSortingTruck(accountId, year, quarter) {
  return request({
     url: '/refuse/sorting_truck/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year,
      quarter
    }
  })
}

// --------------------------------------中转设施--------------------------------------
// 保存中转设施
export function saveTransitFacilities(accountId, list) {
  return request({
     url: '/refuse/transit_facilities',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 中转设施详情
export function getTransitFacilities(accountId) {
  return request({
    url: `/refuse/transit_facilities/${accountId}`,
    method: 'get'
  })
}
// 中转设施分页
export function listTransitFacilities(accountId, pageNum, pageSize, year) {
  return request({
     url: '/refuse/transit_facilities/list',
    method: 'post',
    data: {
      accountId,
      year,
      pageNum,
      pageSize
    }
  })
}
// 中转设施分页详情
export function detailsTransitFacilities(accountId, year) {
  return request({
     url: '/refuse/transit_facilities/record_details',
    method: 'post',
    data: {
      accountId,
      year
    }
  })
}
// 中转设施导出
export function exportTransitFacilities(accountId, year) {
  return request({
     url: '/refuse/transit_facilities/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year
    }
  })
}

// --------------------------------------处理设施--------------------------------------
// 保存处理设施
export function saveProcessingFacilities(accountId, list) {
  return request({
     url: '/refuse/processing_facilities',
    method: 'post',
    data: {
      accountId,
      data: list
    }
  })
}
// 处理设施详情
export function getProcessingFacilities(accountId) {
  return request({
    url: `/refuse/processing_facilities/${accountId}`,
    method: 'get'
  })
}
// 处理设施分页
export function listProcessingFacilities(accountId, pageNum, pageSize, year) {
  return request({
     url: '/refuse/processing_facilities/list',
    method: 'post',
    data: {
      accountId,
      year,
      pageNum,
      pageSize
    }
  })
}
// 处理设施分页详情
export function detailsProcessingFacilities(accountId, year) {
  return request({
     url: '/refuse/processing_facilities/record_details',
    method: 'post',
    data: {
      accountId,
      year
    }
  })
}
// 处理设施导出
export function exportProcessingFacilities(accountId, year) {
  return request({
     url: '/refuse/processing_facilities/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      accountId,
      year
    }
  })
}

// --------------------------------------生活垃圾分类统计指标体系--------------------------------------
// 保存生活垃圾分类统计指标体系
export function saveIndexSystem(data) {
  return request({
     url: '/refuse/index_system',
    method: 'post',
    data
  })
}
// 生活垃圾分类统计指标体系详情
export function getIndexSystem(accountId, type) {
  return request({
    url: `/refuse/index_system/find_current`,
    method: 'get',
    params: {
      accountId,
      type
    }
  })
}
// 生活垃圾分类统计指标体系分页
export function listIndexSystem(accountId, pageNum, pageSize, type, startTime, endTime) {
  return request({
     url: '/refuse/index_system/list',
    method: 'post',
    data: {
      accountId,
      pageNum,
      pageSize,
      type,
      startTime,
      endTime
    }
  })
}
// 生活垃圾分类统计指标体系分页详情
export function detailsIndexSystem(id, accountId, year, month, quarter) {
  return request({
     url: '/refuse/index_system/record_details',
    method: 'post',
    data: {
      id,
      accountId,
      year,
      month,
      quarter
    }
  })
}
// 生活垃圾分类统计指标体系导出
export function exportIndexSystem(id, accountId, year, month, quarter) {
  return request({
     url: '/refuse/index_system/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params: {
      id,
      accountId,
      year,
      month,
      quarter
    }
  })
}
