import request from '@/utils/request'

// 周报 废弃
export function week(data) {
  return request({
    url: '/air/report/week2',
    method: 'post',
    data
  })
}
// 周报 废弃
export function month(data) {
  return request({
    url: '/air/report/month2',
    method: 'post',
    data
  })
}
// 下载空气周简报 废弃
export function getWeekDetail(params) {
  return request({
    url: '/air/report/getWeekDetail',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}
// 下载空气月简报 废弃
export function getMonthDetail(params) {
  return request({
    url: '/air/report/getMonthDetail',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}
export function pageList(params) {
  return request({
    url: '/air/report/pageList',
    method: 'get',
    params
  })
}
