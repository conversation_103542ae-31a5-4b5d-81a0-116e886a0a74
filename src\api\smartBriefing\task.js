import request from '@/utils/request'

/**
 * @method functionName
 * @param {type} data 说明
 * @description 空气质量考核
 */
// eslint-disable-next-line import/prefer-default-export
export function pageList(params) {
  return request({
    url: '/task/task-report/pageList',
    method: 'get',
    params
  })
}
// 自定义导出任务简报
export function genderReport(params) {
  return request({
    url: '/task/task-report/genderReport',
    method: 'get',
    responseType: 'blob',
    tiemout: '30000',
    params
  })
}

