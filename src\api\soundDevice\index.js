import request from '@/utils/request'

/**
 * 获取音柱设备列表
 * @param {Object} data {
    "pageNum": "1",
    "pageSize": "10",
    "keyword": "查询参数",
    "query": {
        "status": "0 离线 1 在线 2 任务中",
        "type": "5111 话筒 1111 音柱"
    }
}
 * @returns request
 */
export function getList(data) {
  const {
    pageNum, pageSize, status, type, keyword
  } = data

  return request({
    url: '/waterSource/pillar/page',
    method: 'post',
    data: {
      pageNum: pageNum || 1,
      pageSize: pageSize || 10,
      keyword,
      query: {
        status,
        type
      }
    }
  })
}

/**
 * 新增音柱设备
 * @param {Object} data 所有参数均为必填项
 * {
    "name": "设备名称",
    "type": "设备类型 5111 话筒 1111 音柱",
    "sn": "设备SN码",
    "lat": "纬度",
    "lng": "经度",
    "stationId": "站点ID",
    "stationName": "站点名称"
}
 * @returns promise
 */
export function add(data) {
  const {
    name,
    type,
    sn,
    lat,
    lng,
    stationId,
    stationName
  } = data

  return request({
    url: '/waterSource/pillar/add',
    method: 'POST',
    data: {
      name,
      type,
      sn,
      lat,
      lng,
      stationId,
      stationName
    }
  })
}

/**
 * 编辑音柱设备
 * @param {Object} data 所有参数均为必填项
 * {
    "id": "设备id",
    "name": "设备名称",
    "type": "设备类型 5111 话筒 1111 音柱",
    "sn": "设备SN码",
    "lat": "纬度",
    "lng": "经度",
    "stationId": "站点ID",
    "stationName": "站点名称"
}
 * @returns promise
 */
export function update(data) {
  const {
    id,
    name,
    type,
    sn,
    lat,
    lng,
    stationId,
    stationName
  } = data

  return request({
    url: '/waterSource/pillar/update',
    method: 'PUT',
    data: {
      id,
      name,
      type,
      sn,
      lat,
      lng,
      stationId,
      stationName
    }
  })
}

/**
 * 删除设备
 * @param {String} id 设备id
 * @returns promise
 */
export function del(id) {
  return request({
    url: `/waterSource/pillar/del?id=${id}`,
    method: 'DELETE'
  })
}

/**
 * 设备操作-重启-静音-取消静音
 * @param {Object} data {
    "operation": "操作",
      RESTART| [string] 重启 | 默认值
      MUTE| [string] 静音
      UN_MUTE| [string] 取消静音
    "id": "设备ID"
}
 * @returns promise
 */
export function set(data) {
  const { operation, id } = data
  return request({
    url: `/waterSource/pillar/operation`,
    method: 'PUT',
    data: {
      operation,
      id
    }
  })
}

export default {}
