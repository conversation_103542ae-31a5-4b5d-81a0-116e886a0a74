/* eslint-disable */
import request from '@/utils/request'
/* 人员管理 */
// 人员列表
export function userList(data) {
  return request({
     url: '/system/user/list',
    method: 'post',
    data
  })
}
// 新增人员
export function addUser(user, arg2, arg3, arg4) {
  return request({
     url: '/system/user',
    method: 'post',
    data: user
  })
}
// 更新人员
export function updataUser(user, arg2, arg3, arg4) {
  return request({
     url: '/system/user',
    method: 'put',
    data: user
  })
}
// 删除人员
export function delUser(id, arg2, arg3, arg4) {
  return request({
    url: `/system/user/${id}`,
    method: 'delete'
  })
}
// 根据部门id获取部门负责人
export function userListId(departmentId, arg2, arg3, arg4) {
  return request({
    url: `/system/user/manage/${departmentId}`,
    method: 'get'
  })
}
// 根据id获取人员详情
export function userIdDetails(id, arg2, arg3, arg4) {
  return request({
    url: `/system/user/${id}`,
    method: 'get'
  })
}
/* 操作日志 */
// 操作日志列表
export function operationList(data) {
  return request({
     url: '/system/operation-log/getEpTaskLogPage',
    method: 'post',
    data
  })
}

/* 角色管理 */
// 角色列表(分页)
export function roleList(pageNum, pageSize, departmentId, userId) {
  return request({
     url: '/system/role/page-list',
    method: 'post',
    data: {
      pageNum,
      pageSize,
      departmentId,
      userId
    }
  })
}
// 角色列表（不分页）
export function roleListAll() {
  return request({
     url: '/system/role/all_role/list',
    method: 'get'
  })
}
// 新增角色
export function addRole(roleName, departmentId, userId, userName, currentDepartmentId) {
  return request({
     url: '/system/role/add',
    method: 'post',
    data: {
      roleName,
      departmentId,
      userId,
      userName,
      currentDepartmentId
    }
  })
}
// 更新角色
export function updateRole(roleId, roleName, userId, userName, currentDepartmentId) {
  return request({
     url: '/system/role/update',
    method: 'put',
    data: {
      roleId,
      roleName,
      userId,
      userName,
      currentDepartmentId
    }
  })
}
// 删除角色
export function delRole(roleId, userId, userName, currentDepartmentId) {
  return request({
     url: '/system/role/delete',
    method: 'delete',
    data: {
      roleId,
      userId,
      userName,
      currentDepartmentId
    }
  })
}
// 为角色授权
export function authorize(roleId, permissionMenuList, currentDepartmentId, userId, userName) {
  return request({
     url: '/system/role/authorize',
    method: 'post',
    data: {
      roleId,
      permissionMenuList,
      currentDepartmentId,
      userId,
      userName
    }
  })
}
// 根据当前所在部门查询当前部门下的角色信息
export function roleListId(departmentId) {
  return request({
    url: `/system/role/list/${departmentId}`,
    method: 'get'
  })
}
// 获取所有权限列表
export function menuListAll() {
  return request({
     url: '/system/system/list/permission_option_menu',
    method: 'get'
  })
}
// 根据角色id获取菜单权限列表
export function menuListAllId(roleId) {
  return request({
    url: `/system/system/list/${roleId}`,
    method: 'get'
  })
}
// 根据角色id 查询包含的菜单权限和 功能权限
export function roleMenuList(roleId) {
  return request({
    url: `/system/role/ep-role/list-menu-by-role/${roleId}`,
    method: 'get'
  })
}

/* 部门管理 */
// 部门列表（不分页）
export function departmentListAll() {
  return request({
     url: '/system/department/list',
    method: 'get'
  })
}
// 部门层级展示列表
// export function departmentList(departmentId) {
//   return request({
//      url: '/department/level_list',
//     method: 'post',
//     headers: {
//       'Content-Type': 'application/x-www-form-urlencoded'
//     },
//     data: qs.stringify({
//       departmentId
//     })
//   })
// }
// 菜单权限
export function getPerssionList(departmentId) {
  return request({
    url: `/system/systemMenu/getMenuListByDepartmentId/${departmentId}`,
    method: 'get'
  })
}

export function departmentList(departmentId) {
  return request({
    url: `/system/department/listTree/${departmentId}`,
    method: 'get'
  })
}
// 新增部门
export function addDepartment(data) {
  return request({
     url: '/system/department/createDepartmentAccount',
    method: 'post',
    data
  })
}
// 修改部门
export function updataDepartment(data) {
  return request({
     url: '/system/department/modifyDepartmentAccount',
    method: 'post',
    data
  })
}
// 删除部门
export function delDepartment(departmentId) {
  return request({
    url: '/system/department/deleteDepartmentAccount/',
    method: 'get',
    params: { departmentId }
  })
}
// 根据上级部门id查询部门集合
export function departmentIdList(accountId) {
  return request({
     url: '/system/department/getDepartmentAccount/',
    method: 'get',
    params: { accountId }
  })
}

// 获取上级部门权限
export function getParentdelDepartment(currentDepartmentId, parentId) {
  return request({
     url: '/system/account/menuList_current_and_parent',
    method: 'get',
    params: { currentDepartmentId, parentId }
  })
}

// 获取人员列表
export function getPersonList(data) {
  return request({
     url: '/system/account/getAccountByDeptId',
    method: 'post',
    data
  })
}

// 根据策略查询日志分页
export function getJournal(data) {
  return request({
     url: '/system/operation-log/getOperationLogPage',
    method: 'post',
    data
  })
}
