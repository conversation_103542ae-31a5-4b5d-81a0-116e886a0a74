/* eslint-disable */
import request from '@/utils/request'

// 获取任务分页列表 分组: 任务管理 负责人: 杨郁沛
/** * taskType  任务类型(1 联动, 2 内部, 3 应急, 4 巡岗)   当前默认2
      pageNum,
      pageSize,
      overdueStatus  逾期状态(0 未逾期, 1 已逾期)
      myTaskType  我的任务类型(1 我发起的, 2 我处理的, 3 抄送我的)
      userId 用户ID
      startDate  开始日期
      endDate 结束日期
      keywords  搜索关键字
      completeStatus 	完成状态(0 进行中, 1 已完成, 2 已退单)
      myCompleteStatus 我的完成状态(0 发起, 1 待办, 2 已办, 3 抄送, 4 关闭)
      **/
export function getTaskPage(params) {
  return request({
    url: `/task/getTaskPage`,
    method: 'get',
    params
  })
}


/* 获取当前账户下的部门 */
export function getDepartment() {
  return request({
    url: `/system/department/departmentList`,
    method: 'get',
  })
}


// 新增任务 分组: 任务管理 负责人: 杨郁沛
export function addTask(data) {
  return request({
    url: `/task/createTask`,
    method: 'post',
    timeout: 300000,
    data
  })
}

// 获取预案列表 分组: 任务管理 负责人: 杨郁沛
export function getTopTaskExecutorList() {
  return request({
    url: `/system/department/getTopTaskExecutorList`,
    method: 'get',
  })
}

// 获取局内执行者列表 分组: 任务管理 负责人: 杨郁沛
export function getPersonList(departmentId, withStreet) {
  return request({
    url: `/system/department/getTaskExecutorList`,
    method: 'get',
    params: {
      departmentId,
      withStreet
    }
  })
}

/**
 * @method functionName
 * @param {taskType} 任务类型
 * @param {completeStatus} 任务状态
 * @param {myTaskType} 处理类型
 * @param {userId} 用户id
 * @param {startDate} 开始日期
 * @param {endDate} 结束日期
 * @param {keywords} 关键词
 * @param {overdueStatus} 逾期状态
 * @description 导出我的任务表格
 */
 export function exportExcel(params) {
  return request({
    url: `/task/export`,
    method: 'get',
    responseType: 'blob',
    params
  })
}

// 任务详情 分组: 任务管理 负责人: 杨郁沛
export function taskDetail({taskId, userId}) {
  return request({
    url: `/task/getTaskDetail`,
    method: 'get',
    params: {
      taskId,
      userId
    }
  })
}
// 指派节点 分组: 任务管理 负责人: 杨郁沛
export function assignNodeTask(data) {
  return request({
    url: `/task/node/assign`,
    method: 'post',
    // 设置延迟时间
    timeout: 30000,
    data
  })
}
// 关闭任务 分组: 任务管理 负责人: 张金
export function closeTask(taskId, userId, completeRemark) {
  return request({
     url: '/task/close',
    method: 'post',
    data: {
      taskId,
      userId,
      completeRemark
    }
  })
}

// 完成任务 分组: 任务管理 负责人: 杨郁沛
export function completeTask(taskId, userId, completeRemark, taskAnnexList) {
  return request({
     url: '/task/complete',
    method: 'post',
    data: {
      taskId,
      userId,
      completeRemark,
      taskAnnexList
    }
  })
}

// 开始任务 分组: 任务管理 负责人: 张金
export function startTask(accountId, userName, taskId, arg4) {
  return request({
     url: '/task/start',
    method: 'post',
    data: {
      accountId,
      userName,
      taskId
    }
  })
}
