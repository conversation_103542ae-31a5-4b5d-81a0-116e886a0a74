import request from '@/utils/request'
// 获取任务类型统计
export function getTypeStatisticalComparisonList(cycle, startDate, endDate) {
  return request({
    url: '/task/getTypeStatisticalComparisonList',
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}
// 导出部门联动数据
export function exportExceldata(cycle, startDate, endDate) {
  return request({
    url: `/task/taskStatisticsExport?startDate=${startDate}&endDate=${endDate}`,
    method: 'get',
    responseType: 'blob'
  })
}
// 获取任务完成情况
export function getTaskCompletionList(cycle, startDate, endDate) {
  return request({
    url: '/task/getTaskCompletionList',
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}
// 获取分类统计
export function getTypeStatisticsList(cycle, startDate, endDate) {
  return request({
    url: '/task/getTypeStatisticsList',
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}
// 获取联动统计
export function getLinkageTaskList(cycle, startDate, endDate) {
  return request({
    url: '/task/getLinkageTaskList',
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}
// 获取部门统计
export function getDepartmentTaskStatisticsList(cycle, startDate, endDate) {
  return request({
    url: '/task/getDepartmentTaskStatisticsList',
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}
