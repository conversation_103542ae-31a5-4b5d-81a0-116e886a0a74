import request from '@/utils/request'

export function taskTrackCount() {
  return request({
    url: '/air/eps/count',
    method: 'get'
  })
}

export function taskTrackDetail(params) {
  return request({
    url: '/air/eps/pageDetail',
    method: 'post',
    params
  })
}

export function taskTrackPageList(params) {
  return request({
    url: '/air/eps/pageList',
    method: 'post',
    params
  })
}
// 导出数据
export function taskTrackexport(params) {
  return request({
    url: '/air/eps/export',
    method: 'get',
    'Content-Type': 'multipart/form-data',
    responseType: 'blob',
    params
  })
}
