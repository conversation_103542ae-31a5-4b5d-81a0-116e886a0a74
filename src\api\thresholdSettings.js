import request from '@/utils/request'

// 获取噪音阈值列表
export function noiseStationList(data) {
  return request({
    url: '/water/noise-threshold/list',
    method: 'get',
    params: data
  })
}
// 修改噪音阈值
export function updateNoiseThreshold(data) {
  return request({
    url: '/water/noise-threshold/updateThreshold',
    method: 'put',
    data
  })
}
// 获取所有用电企业列表
export function electricityAllStationList(data) {
  return request({
    url: '/water/electricity/listStation',
    method: 'get',
    params: data
  })
}
// 获取分页用电企业列表
export function electricityStationList(data) {
  return request({
    url: '/water/electricity/pageStation',
    method: 'get',
    params: data
  })
}
// 修改用电阈值
export function updateElectricityThreshold(data) {
  return request({
    url: '/water/electricity/updateAlarmThreshold',
    method: 'put',
    data
  })
}
// 获取所有固废列表
export function wasteAllStationList(data) {
  return request({
    url: '/water/waste-solid-device/listStation',
    method: 'get',
    params: data
  })
}
// 获取分页固废列表
export function wasteStationList(data) {
  return request({
    url: '/water/waste-solid-device/pageStation',
    method: 'get',
    params: data
  })
}
// 修改固废阈值
export function updateWasteThreshold(data) {
  return request({
    url: '/water/waste-solid-device/updateThreshold',
    method: 'put',
    data
  })
}
// 获取所有建筑工地列表
export function constructionAllSiteList(data) {
  return request({
    url: '/air/company/list',
    method: 'get',
    data
  })
}
// 获取分页建筑工地列表
export function constructionSiteList(data) {
  return request({
    url: '/air/company/page_list',
    method: 'post',
    data
  })
}
// 修改建筑 工地阈值
export function updateConstructionThreshold(data) {
  return request({
    url: '/air/company/updateThreshold',
    method: 'put',
    data
  })
}
// 获取所有餐饮油烟列表
export function restaurantAllSiteList(data) {
  return request({
    url: '/water/restaurant/list',
    method: 'get',
    data
  })
}
// 获取分页餐饮油烟列表
export function restaurantSiteList(data) {
  return request({
    url: '/water/restaurant/page',
    method: 'post',
    data
  })
}
// 修改餐饮油烟阈值
export function updateRestaurantThreshold(data) {
  return request({
    url: '/water/restaurant/updateAlarmThreshold',
    method: 'put',
    data
  })
}
// 获取所有印刷企业列表
export function printAllSiteList(data) {
  return request({
    url: '/water/print/list',
    method: 'get',
    data
  })
}
// 获取分页印刷企业列表
export function printSiteList(data) {
  return request({
    url: '/water/print/pageList',
    method: 'post',
    data
  })
}
// 修改印刷企业阈值
export function updatePrintThreshold(data) {
  return request({
    url: '/water/print/updateAlarmThreshold',
    method: 'put',
    data
  })
}
// 获取所有汽修厂列表
export function garageAllSiteList(data) {
  return request({
    url: '/water/garage/list',
    method: 'get',
    data
  })
}
// 获取分页汽修厂列表
export function garageSiteList(data) {
  return request({
    url: '/water/garage/pageList',
    method: 'post',
    data
  })
}
// 修改汽修厂阈值
export function updateGarageThreshold(data) {
  return request({
    url: '/water/garage/updateAlarmThreshold',
    method: 'put',
    data
  })
}
// 获取所有排污企业列表
export function HPEAllSiteList(data) {
  return request({
    url: '/water/sewage/enterprise/list',
    method: 'get',
    params: {}
  })
}
// 获取分页排污企业列表
export function HPESiteList(data) {
  return request({
    url: '/water/heavily-polluting-enterprise-alarm-item/pageList',
    method: 'get',
    params: data
  })
}
// 修改排污企业阈值
export function updateHPEThreshold(data) {
  return request({
    url: '/water/heavily-polluting-enterprise-alarm-item/batchSaveOrUpdate',
    method: 'post',
    data
  })
}
// 分页获取激光雷达阈值列表
export function lidarStationList(params) {
  return request({
    url: '/air/web/lidar-station/pageList',
    method: 'get',
    params
  })
}
// 不分页获取激光雷达阈值列表
export function lidarStationAllList() {
  return request({
    url: '/air/web/lidar-station/list',
    method: 'get'
  })
}
// 修改激光雷达阈值
export function updateLidarThreshold(params) {
  return request({
    url: '/air/web/lidar-station/updateThreshold',
    method: 'put',
    data: params
  })
}
