import request from '@/utils/request'

/** 升级包列表 */
export function getList(data) {
  return request({
    url: '/waterSource/client-package/page',
    method: 'post',
    data
  })
}

/** 新增 */
export function savePackage(data) {
  return request({
    url: '/waterSource/client-package/save',
    method: 'post',
    data
  })
}
/** 编辑 */
export function updatePackage(data) {
  return request({
    url: '/waterSource/client-package/update',
    method: 'put',
    data
  })
}
/** 删除 */

export function deletePackage({ id }) {
  return request({
    url: `/waterSource/client-package/delete?ids=${id}`,
    method: 'DELETE'

  })
}
