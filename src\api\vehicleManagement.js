import request from '@/utils/request'
// import { method } from 'lodash'
// import qs from 'qs'
// 新增车辆
export function addCar(typeId, license, deptId, name, gps) {
  return request({
    url: '/car/add',
    method: 'post',
    data: {
      typeId,
      license,
      deptId,
      name,
      gps
    }
  })
}
// 删除车辆
export function delCar(carId) {
  return request({
    url: '/car/delete',
    method: 'post',
    data: {
      carId
    }
  })
}
// 更新车辆
export function updateCar(carId, typeId, license, deptId, name, gps) {
  return request({
    url: '/car/update',
    method: 'post',
    data: {
      carId,
      typeId,
      license,
      deptId,
      name,
      gps
    }
  })
}
// 获取车辆列表
// export function carList(data) {
//   return request({
//      url: '/web/car/getList',
//     method: 'post',
//     data: data
//   })
// }
export function carList(data) {
  return request({
    url: '/car/manager/findAllCarInfo',
    method: 'post',
    data
  })
}
// 设置电子围栏
export function bindFence(data) {
  return request({
    url: '/car/bindFence',
    method: 'post',
    data
  })
}
// 清除电子围栏
export function unbindFence(fenceId) {
  return request({
    url: '/car/unbindFence',
    method: 'post',
    data: {
      fenceId
    }
  })
}
// 部门层级展示列表
// export function departmentIdList(departmentId, deptName) {
//   return request({
//      url: '/car/getDeptList',
//     method: 'get',
//     headers: {
//       'Content-Type': 'application/x-www-form-urlencoded'
//     },
//     data: qs.stringify({
//       departmentId,
//       deptName
//     })
//   })
// }
// 查询所有部门
export function departmentIdList() {
  return request({
    url: '/car/manager/findAllDept',
    method: 'get'
  })
}
export function getCarType() {
  return request({
    url: '/car/getCarTypeList',
    method: 'get'
  })
}
// 导出车辆信息Excel
export function exportExcelAll({ carType, deptId, carBrand }) {
  return request({
    url: `/car/manager/queryExportCarInfo`,
    method: 'get',
    responseType: 'blob',
    params: {
      carType,
      deptId,
      carBrand
    }
  })
}
// 获取车辆最新位置
export function positionDetailsNew(carBrand) {
  return request({
    url: `/car/manager/getCarLatestLocation/${carBrand}`,
    method: 'get'
  })
}
