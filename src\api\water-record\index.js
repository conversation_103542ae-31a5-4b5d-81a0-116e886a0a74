import request from '@/utils/request'

/**
 * 获取一源一档列表
 * @param {Object} data {
    pageNum,
    pageSize,
    name, 名称
    stationId 站点id
    type 类型
  }
 * @returns promise
 */
export function getRecordList(data) {
  const {
    pageNum, pageSize, name, stationId, type
  } = data

  return request({
    url: '/waterSource/company-file/page',
    method: 'post',
    data: {
      pageNum: pageNum || 1,
      pageSize: pageSize || 9,
      query: {
        stationId,
        name,
        type
      }
    }
  })
}

/**
 * 新建档案
 * @param {Object} data 所有参数均为必填项
 * {
    "name": "单位名",
    "userName": "负责人",
    "phone": "电话（限制11位）",
    "stationId": "监测站id",
    "stationName": "监测站名",
    "address": "地址",
    "lat": "纬度",
    "lng": "经度",
    "type": "单位类型"
}
 * @returns promise
 */
export function addRecord(data) {
  const {
    name,
    userName,
    phone,
    stationId,
    stationName,
    address,
    lat,
    lng,
    type
  } = data

  return request({
    url: '/waterSource/company-file/save',
    method: 'post',
    data: {
      name,
      userName,
      phone,
      stationId,
      stationName,
      address,
      lat,
      lng,
      type
    }
  })
}

/**
 * 修改档案
 * @param {Object} data 所有参数均为必填项
 * {
   "id": "id",
    "name": "单位名",
    "userName": "负责人",
    "phone": "电话（限制11位）",
    "stationId": "监测站id",
    "stationName": "监测站名",
    "address": "地址",
    "lat": "纬度",
    "lng": "经度",
    "type": "单位类型"
}
 * @returns promise
 */
export function updateRecord(data) {
  const {
    id,
    name,
    userName,
    phone,
    stationId,
    stationName,
    address,
    lat,
    lng,
    type
  } = data

  return request({
    url: '/waterSource/company-file/update',
    method: 'PUT',
    data: {
      id,
      name,
      userName,
      phone,
      stationId,
      stationName,
      address,
      lat,
      lng,
      type
    }
  })
}

/**
 * 删除档案
 * @param {Number|String} id 档案id
 * @returns promise
 */
export function deleteRecord(id) {
  return request({
    url: `/waterSource/company-file/delete?id=${id}`,
    method: 'DELETE'
  })
}
export default {}
