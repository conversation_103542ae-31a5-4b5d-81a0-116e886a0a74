import request from '@/utils/request'

// 副本-水位监测趋势图
// eslint-disable-next-line import/prefer-default-export
export function lastTwentyHour() {
  return request({
    url: '/waterSource/waterGauge/lastTwentyHour',
    method: 'get'
  })
}

// 水位告警记录最新10条
// eslint-disable-next-line import/prefer-default-export
export function listGaugeRecordList() {
  return request({
    url: '/water/water-source-alarm/listGaugeRecordList',
    method: 'get'
  })
}

// 水源地取水量 水源地达标率
// eslint-disable-next-line import/prefer-default-export
export function listMonthRecord() {
  return request({
    url: '/waterSource/water-volume-quality/listMonthRecord',
    method: 'get'
  })
}

// 获取水五厂站点信息
// eslint-disable-next-line import/prefer-default-export
export function waterFiveStation() {
  return request({
    url: '/water/station/waterFiveStation',
    method: 'get'
  })
}

// 获取水尺信息
// eslint-disable-next-line import/prefer-default-export
export function getRel() {
  return request({
    url: '/waterSource/waterGauge/getRel',
    method: 'get'
  })
}
// 河流每日总流量统计
// eslint-disable-next-line import/prefer-default-export
export function listWaterFlow() {
  return request({
    url: '/water/station/listWaterFlow',
    method: 'get'
  })
}

// 获取水量数据
// eslint-disable-next-line import/prefer-default-export
export function upToDate() {
  return request({
    url: '/waterSource/water-volume-quality/listMonthRecordByAll',
    method: 'get'
  })
}
