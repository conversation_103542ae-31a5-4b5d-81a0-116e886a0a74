import request from '@/utils/request'

// 获取河流列表
export function getRiverList(districtCode) {
  return request({
    url: '/water/monitor/getRiverList',
    method: 'get',
    params: {
      districtCode
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
// 获取历史数据table
export function getTableList(data) {
  return request({
    url: '/water/management/getRealtimeMonitorPage',
    method: 'post',
    data
  })
}
/**
 * 获取实时监测数据
 * @param Object args
 * @param {*} pageNum 分页页数
 * @param {*} pageSize 分页大小
 * @param {*} keywords 关键字*
 * @param {*} stationId 站点id*
 * @param {*} riverId 河流id*
 */
export function getTableNewList(data) {
  return request({
    url: '/water/pageList',
    method: 'post',
    data
  })
}
// 导出历史数据表
export function exportExcel(data) {
  return request({
    url: '/water/management/exportRealtimeMonitor',
    method: 'post',
    'Content-Type': 'multipart/form-data',
    responseType: 'blob',
    data
  })
}
// 获取报警数据table
export function getAlarmTableList(data) {
  return request({
    url: '/management/water/getWaterAlarmPage',
    method: 'post',
    data
  })
}
// 导出报警数据table
export function exportAlarmExcel(data) {
  return request({
    url: '/management/water/exportWaterAlarm',
    method: 'post',
    data
  })
}

/**
 * 获取今年考核表
 * @returns request
 */
export function getAppraisalTarget() {
  return request({
    url: '/water/water-assessment-goal/listThisYear'
  })
}

/**
 * 新增或修改考核目标
 * @param {Array} data 数组包对象 数据结构和getAppraisalTarget获取的一样
 * @returns request || false
 */
export function saveAppraisalTarget(data) {
  return request({
    url: '/water/water-assessment-goal/saveBatch',
    method: 'post',
    data
  })
}

/**
 * 获取水质站点信息
 * @returns request
 */
export function getWaterSiteInfo() {
  return request({
    url: '/water/monitor_report?type=1'
  })
}

/**
 * 获取水质站点信息
 * @param {Object} data
 * {
    "stationId": "站点",
    "waterType": "水质类型"
}
 * @returns {Promise || Boolean} request || false
 */
export function saveWaterSiteInfo(data) {
  const {
    stationId,
    waterType
  } = data
  const noParams = !stationId || !waterType

  if (noParams) return false

  return request({
    url: '/water/updateWaterType',
    method: 'put',
    data: {
      stationId,
      waterType
    }
  })
}

// 获取录像列表
export function playbackList(params) {
  return request({
    url: '/water/water-camera/playbackPageList',
    method: 'get',
    params
  })
}

export function playbackListGarage(params) {
  return request({
    url: '/water/garage-camera/playbackPageList',
    method: 'get',
    params
  })
}

// 获取录像地址
export function playbackStart(params) {
  return request({
    url: '/water/water-camera/playbackStart',
    method: 'get',
    params
  })
}

export function playbackStartGarage(params) {
  return request({
    url: '/water/garage-camera/playbackStart',
    method: 'get',
    params
  })
}

// 获取河流列表
export function riverList(params) {
  return request({
    url: '/water/river/list',
    method: 'get',
    params
  })
}
// 根据河流获取断面列表
export function listByRiver(params) {
  return request({
    url: '/water/cross-section/listByRiver',
    method: 'get',
    params
  })
}
// 分页查询 水质考核数据列表
export function pageList(params) {
  return request({
    url: '/water/water-assessment-goal-month/pageList',
    method: 'get',
    params
  })
}
// 检查新增月度考核数据是否存在
export function checkSave(data) {
  return request({
    url: '/water/water-assessment-goal-month/checkSave',
    method: 'post',
    data
  })
}
// 新增月度考核数据
export function save(data) {
  return request({
    url: '/water/water-assessment-goal-month/save',
    method: 'post',
    data
  })
}
// 修改月度考核数据
export function update(data) {
  return request({
    url: '/water/water-assessment-goal-month/update',
    method: 'put',
    data
  })
}
// 导出年度考核目标
export function exportYear() {
  return request({
    url: '/water/water-assessment-goal/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob'
  })
}
// 导出年度考核目标
export function exportPageList(params) {
  return request({
    url: '/water/water-assessment-goal-month/export',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    responseType: 'blob',
    params
  })
}
// 修改月度考核数据
export function deleteMonth(id) {
  return request({
    url: `/water/water-assessment-goal-month/delete?id=${id}`,
    method: 'delete'
  })
}
// 获取断面趋势
export function listByTrend(params) {
  return request({
    url: '/water/water-assessment-goal-month/trend',
    method: 'get',
    params
  })
}
