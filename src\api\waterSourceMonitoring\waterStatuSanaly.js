import request from '@/utils/request'
/**
 * 水质同环比
 */
export function getalarmCompare() {
  return request({
    url: `/water/quality/alarmCompare`,
    method: 'get'
  })
}
/**
 * 污染监测趋势
 * @returns
 */
export function getMonitoring(data) {
  const url = data.begin ? `/water/quality/pollutionTrendMonitoring?type=${data.type}&begin=${data.begin}&end=${data.end}` : `/water/quality/pollutionTrendMonitoring?type=${data.type}`
  return request({
    url,
    method: 'get'
  })
}

/**
 * 指标类别
 * @param {object} data
 */
export function getmetricTypeAg() {
  return request({
    url: `/water/quality/metricTypeAggregation`,
    method: 'get'
  })
}
/**
 *
水质类型（近一年数据）
 */
export function getwaterType() {
  return request({
    url: `/water/quality/waterQualityCategory`,
    method: 'get'
  })
}
/**
 * 污染日历
 */
export function getmonth(month) {
  return request({
    url: `/water/quality/polluteDate?month=${month}`,
    method: 'get'
  })
}
/**
 * 告警排行
 * type = 1 月
 * type = 2 年
 */
export function getAlarmRank(type) {
  return request({
    url: `/water/quality/stationAlarmRank?type=${type}`,
    method: 'get'
  })
}
