import request from '@/utils/request'

// 沙河下所有站点
export function getStation() {
  return request({
    url: '/water/station/shaheStation',
    method: 'get'
  })
}

// 站点告警记录-今日
/**
 *
 * @param {站点id} id
 * @returns
 */
export function getAlarmRecord(id) {
  return request({
    url: `/water/quality/todayAlarmRecord?stationId=${id}`,
    method: 'get'
  })
}

// 趋势监测-当天
/**
 *
 * @param {站点id} id
 * @returns
 */
export function getMonitorTrend(id) {
  return request({
    url: `water/quality/todayMonitorTrends?stationId=${id}`,
    method: 'get'
  })
}

// 站点下的摄像机
/**
 *
 * @param {object} data
 * @returns
 */
export function getlistMonitor(data) {
  return request({
    url: `/water/water-camera/listMonitor`,
    method: 'post',
    data
  })
}

/**
 * 获取直播流地址
 * @param {通道id} channelId
 * @returns
 */
export function getliveUrl(channelId) {
  return request({
    url: `/water/water-camera/live/${channelId}`,
    method: 'get'
  })
}

