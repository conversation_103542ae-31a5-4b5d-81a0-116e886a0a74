<template>
  <div
    :class="className"
    :style="{height:height,width:width}"
  />
</template>

<script>
import echarts from 'echarts' // echarts theme
import resize from './mixins/resize'
import autoplay from './mixins/autoPlay'

require('echarts/theme/macarons')

export default {
  mixins: [resize, autoplay],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    doAutoplay: {
      type: Boolean,
      default: true
    },
    legendList: {
      type: Array,
      default: () => []
    },
    seriesData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    seriesData() {
      this.$nextTick(() => {
        this.initChart()
        // this.autoPlayTool(this.chart, this.seriesData.dataList, 0)
      })
    }
  },
  created() {
    this.graceRequestAnimationFrame()
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      // this.autoPlayTool(this.chart, this.seriesData.dataList, 0)
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.showLoading({
        text: '加载中',
        color: '#fff',
        textColor: '#fff',
        maskColor: 'rgba(3,169,255,0.1)',
        zlevel: 10
      })
      this.chart.setOption({
        title: {
          left: 'center',
          top: 0,
          text: '空气优良天数',
          textStyle: {
            fontSize: 12,
            color: '#333',
            fontWeight: 'bold'
          }
        },
        color: this.seriesData.colorList,
        tooltip: {
          trigger: 'item',
          confine: true,
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '5%',
          top: '10%',
          containLabel: true
        },
        legend: {
          show: this.showLegend,
          left: 'center',
          bottom: '10',
          data: this.legendList
        },
        series: [
          {
            name: this.seriesData.name,
            type: 'pie',
            // roseType: 'radius',
            label: {
              show: false
            },
            radius: [45, 65],
            center: ['50%', '50%'],
            data: this.seriesData.dataList,
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      })
      this.chart.hideLoading()
    }
  }
}
</script>
