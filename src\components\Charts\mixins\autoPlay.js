export default {
  methods: {
    /**
     * @param {chartIns} echarts实例
     * @param {chartDate} echarts数据
     * @param {curIndex} 当前要展示的数据index
     */
    autoPlayTool(chartIns, chartDate, curIndex, seriesIndex = 0, speed = 1000) {
      let chartsTimer = 0
      const setpfun = () => {
        chartsTimer += 15
        if (chartsTimer > speed) {
          chartsTimer = 0
          chartIns.dispatchAction({
            type: 'downplay',
            seriesIndex,
            dataIndex: curIndex - 1 < 0 ? chartDate.length - 1 : curIndex - 1
          })
          chartIns.dispatchAction({
            type: 'highlight',
            seriesIndex,
            dataIndex: curIndex
          })
          chartIns.dispatchAction({
            type: 'showTip',
            seriesIndex,
            dataIndex: curIndex
          })
          curIndex === chartDate.length - 1 ? curIndex = 0 : curIndex++
        }
        window.requestAnimationFrame(setpfun)
      }
      window.requestAnimationFrame(setpfun)
      chartIns.on('mouseover', () => {
        window.cancelAnimationFrame(setpfun)
      })
      chartIns.on('mouseout', () => {
        // noop
      })
    },
    graceRequestAnimationFrame() {
      if (!Date.now) { Date.now = function() { return new Date().getTime() } }

      (function() {
        const vendors = ['webkit', 'moz']
        for (let i = 0; i < vendors.length && !window.requestAnimationFrame; ++i) {
          const vp = vendors[i]
          window.requestAnimationFrame = window[`${vp}RequestAnimationFrame`]
          window.cancelAnimationFrame = (window[`${vp}CancelAnimationFrame`] ||
                                   window[`${vp}CancelRequestAnimationFrame`])
        }
        if (/iP(ad|hone|od).*OS 6/.test(window.navigator.userAgent) || // iOS6 is buggy
        !window.requestAnimationFrame || !window.cancelAnimationFrame) {
          let lastTime = 0
          window.requestAnimationFrame = function(callback) {
            const now = Date.now()
            const nextTime = Math.max(lastTime + 16, now)
            return setTimeout(() => { callback(lastTime = nextTime) },
              nextTime - now)
          }
          window.cancelAnimationFrame = clearTimeout
        }
      }())
    }
  }
}
