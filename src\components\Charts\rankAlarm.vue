<template>
  <div>
    <div
      :id="id"
      :class="className"
      :style="{height:height,width:width}"
    />
  </div>
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

export default {
  name: 'RankAlarm',
  components: {

  },
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    propData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
  },
  watch: {
    propData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  created() {

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const data = this.propData
      const titleArr = []
      const seriesArr = []
      const graphicArr = []
      // const tooltipArr = []
      const colors = ['#13CE66', '#1A9197', '#FFCF27', '#FB9B5A', '#31A4FF']
      data.forEach((item, index) => {
        titleArr.push(
          {
            subtext: item.value || '0',
            text: '总数',
            textStyle: {
              color: '#000',
              fontSize: 14
            },
            subtextStyle: {
              color: '#3BB66F',
              fontSize: 14
            },
            left: `${index * 33.3 + 10}%`,
            top: '27%',
            textAlign: 'center'
          }
        )
        // tooltipArr.push({
        //   trigger: 'axis',
        //   show: true
        // })
        graphicArr.push(
          {
            type: 'text',
            // eslint-disable-next-line no-nested-ternary
            left: `${!index ? 5 : index === 1 ? 38 : 74}%`,
            top: '68%',
            style: {
              text: item.name,
              textAlign: 'center',
              fill: '#333',
              fontSize: 16,
              fontWeight: 500
            }
          }
        )
        seriesArr.push(
          {
            name: item.name,
            type: 'pie',
            clockWise: false,
            radius: [46, 70],
            center: [`${index * 33.3 + 11}%`, '35%'],
            itemStyle: {
              normal: {
                // 边框
                borderWidth: 3,
                borderColor: '#ffffff',
                color(params) {
                  return colors[params.dataIndex]
                }

              }
            },
            label: {
              normal: {
                show: false
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: item.data
          }
        )
      })
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption({
        backgroundColor: '#fff',
        title: titleArr,
        series: seriesArr,
        graphic: graphicArr,
        tooltip: {
          trigger: 'item',
          formatter(params) {
            const res1 = `<div>告警站点: ${params.seriesName}</div>
                          <div>告警类型: ${params.name}</div>
                          <div>告警次数: ${params.value}次</div>
                          <div>告警比例: ${params.percent}%</div>
          `
            return res1
          },
          show: true
        }
      })
      this.chart.hideLoading()
    }
  }
}
</script>

<style lang="scss">
</style>
