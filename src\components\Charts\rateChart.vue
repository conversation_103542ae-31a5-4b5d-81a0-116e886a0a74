<template>
  <div>
    <div
      :id="id"
      :class="className"
      :style="{ height: height, width: width }"
    />
  </div>
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

export default {
  name: 'RateChart',
  components: {},
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '240px'
    },
    timeData: {
      type: Array,
      default: () => [1, 2, 3, 4, 5]
    },
    sourceDate: {
      type: Array,
      default: () => [1, 2, 3, 4, 5]
    },
    lineData: {
      type: Array,
      default: () => [0, 0, 0, 0, 0]
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {},
  watch: {
    lineData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
    // this.$nextTick(() => {
    //   this.initChart()
    // })
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption({
        grid: {
          top: '5%',
          left: '2%',
          right: '1%',
          bottom: '10%',
          containLabel: true
        },
        legend: {
          data: ['监测趋势'],
          show: false,
          top: '4%',
          left: '8%'
        },
        tooltip: {
          trigger: 'axis',
          position: 'top',
          formatter: (params) => {
            const data = params[0]
            const str = `
              <div>${this.sourceDate[data.dataIndex]}</div>
              <div>${data.marker} 水位监测：${data.value} cm</div>
            `
            return str
          }
        },
        xAxis: {
          data: [...this.timeData].map((v) => v.slice(11, 16)),
          linestyle: {
            color: '#ccc'
          },
          axisLine: {
            // 坐标轴轴线相关设置。数学上的x轴
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            rotate: 45,
            // interval: 0,
            // 坐标轴刻度标签的相关设置
            textStyle: {
              color: '#ccc',
              margin: 15
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          name: '单位：cm',
          color: '#ccc',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            color(e) {
              return '#ccc'
            }
          }
        },
        series: [
          {
            name: '监测趋势',
            smooth: true,
            abel: {
              show: true,
              position: 'top',
              textStyle: {
                color: '#fff'
              }
            },
            itemStyle: {
              normal: {
                color: 'rgba(30, 181, 198, 1)'
              }
            },
            lineStyle: {
              normal: {
                color: 'rgba(30, 181, 198, 1)' // 线条颜色
              }
            },
            type: 'line',
            data: [...this.lineData]
          }
        ]
      })
      this.chart.hideLoading()
    }
  }
}
</script>

<style scoped lang="scss">
</style>
