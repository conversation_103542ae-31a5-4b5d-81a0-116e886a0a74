<template>
  <div>
    <div
      :id="id"
      :class="className"
      :style="{ height: height, width: width }"
    />
  </div>
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

export default {
  name: 'TakeWaterChart',
  components: {},
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    propData: {
      type: Array,
      default: () => []
    },
    takeWater: {
      type: Array,
      default: () => []
    },
    targetWaterVolume: {
      type: Array,
      default: () => []
    },
    timeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {},
  watch: {
    timeData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  created() {},
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption({
        grid: {
          top: '20%',
          left: '5%',
          right: '1%',
          bottom: '0%',
          containLabel: true
        },
        legend: {
          data: ['目标取水量', '实际取水量'],
          show: true,
          padding: 5,
          itemWidth: 10,
          itemHeight: 5,
          top: '3%',
          right: '0%',
          textStyle: { color: '#999999', fontSize: 10 }
        },
        tooltip: {
          trigger: 'item',
          position: 'top',
          confine: true
        },
        xAxis: {
          data: [...this.timeData],
          linestyle: {
            color: '#ccc'
          },
          axisLine: {
            // 坐标轴轴线相关设置。数学上的x轴
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            // 坐标轴刻度标签的相关设置
            rotate: 45,
            textStyle: {
              color: '#ccc',
              margin: 15
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          name: '单位：m3',
          color: '#ccc',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            color(e) {
              return '#ccc'
            },
            formatter: '{value}',
            show: true
          }
        },
        series: [
          {
            name: '目标取水量',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#13CE66',
                barBorderRadius: [5, 5, 0, 0]
              }
            },
            barWidth: 8,
            data: [...this.targetWaterVolume]
          },
          {
            name: '实际取水量',
            itemStyle: {
              normal: {
                color: '#FB9B5A',
                barBorderRadius: [5, 5, 0, 0]
              }
            },
            barWidth: 8,
            type: 'bar',
            data: [...this.takeWater]
          }
        ]
      })
      this.chart.hideLoading()
    }
  }
}
</script>

<style lang="scss">
</style>
