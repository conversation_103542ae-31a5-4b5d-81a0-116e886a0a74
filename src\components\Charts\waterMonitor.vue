<template>
  <div>
    <div
      :id="id"
      :class="className"
      :style="{ height: height, width: width }"
    />
  </div>
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

export default {
  name: 'WaterMonitor',
  components: {},
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    propData: {
      type: Array,
      default: () => []
    },
    typeName: {
      type: String,
      default: '氨氮'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {},
  watch: {
    propData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  created() {},
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const { typeName } = this
      if (!this.propData[0]) return
      const color = [
        'rgba(30, 181, 198, 1)',
        'rgba(252, 168, 21, 1)',
        'rgba(49, 164, 255, 1)',
        'rgba(255, 120, 102, 1)',
        'rgba(135, 216, 219, 1)'
      ]
      const xdata = []
      const legenddata = []
      const series = []
      this.propData[0].trends.forEach((item) => {
        xdata.push(item.x)
      })
      this.propData.forEach((item, index) => {
        legenddata.push(item.name)
        const data = item.trends.map((child) => ({
          value: child.y ? child.y : '',
          symbolSize: child.overflow ? 8 : 5,
          symbol: child.overflow ? 'circle' : 'emptyCircle',
          itemStyle: {
            normal: {
              color: child.overflow ? '#ED535B' : color[index], // 拐点颜色
              borderColor: color[index],
              borderWidth: child.overflow ? 0 : 1
            }
          }
        }))
        const obj = {
          name: item.name,
          smooth: true,
          abel: {
            show: true,
            position: 'top',
            textStyle: {
              color: '#fff'
            }
          },
          itemStyle: {
            normal: {
              color: color[index]
            }
          },
          lineStyle: {
            normal: {
              color: color[index] // 线条颜色
            }
          },
          type: 'line',
          connectNulls: true,
          data,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: color[index].replace('1)', '0.2)')
              },
              {
                offset: 1,
                color: 'rgba(53, 163, 173, 0)'
              }
            ])
          }
        }
        series.push(obj)
      })
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption({
        grid: {
          top: '10%',
          left: '2%',
          right: '1%',
          bottom: '0%',
          containLabel: true
        },

        color: ['#80FFA5', '#00DDFF', '#37A2FF', '#FF0087', '#FFBF00'],
        tooltip: {
          // trigger: 'axis'
          trigger: 'axis',
          show: true,
          backgroundColor: '#FFFFFF',
          textStyle: '#000000',
          extraCssText: 'box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.3);',
          formatter(params) {
            const noVal = params.every((item) => !item.value)
            if (noVal) return ''

            const res1 = `<div>监测时间: ${params[0].axisValue}</div>
                                    <div>监测指标：${typeName}</div>
                      `
            let res2 = ''
            params.forEach((item, index) => {
              res2 += `
                          <div class="paramsrow">
                            <div>
                            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color[index]}"></span>
                            <span style="color:${item.color === '#ED535B' ? '#ED535B' : '#000000'}">${item.seriesName}: ${item.value || '--'}${typeName === 'PH' ? '' : 'mg/L'}</span>
                            <div>
                          <div>
                        `
            })
            return res1 + res2
          }
        },
        legend: {
          data: legenddata,
          show: true,
          top: '0%',
          right: '5%'
        },
        xAxis: {
          data: xdata,
          linestyle: {
            color: '#ccc'
          },
          axisLine: {
            // 坐标轴轴线相关设置。数学上的x轴
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            // 坐标轴刻度标签的相关设置
            textStyle: {
              color: '#ccc',
              margin: 15
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          name: `单位：${this.typeName.toUpperCase() === 'PH' ? '' : 'mg/L'}`,
          color: '#ccc',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            color(e) {
              return '#ccc'
            },
            formatter: '{value}',
            show: true
          }
        },
        series
      })
      this.chart.hideLoading()
    }
  }
}
</script>

<style lang="scss">
.paramsrow {
  // display: flex;
  // height: 10px;
}
.paramyuan {
}
</style>
