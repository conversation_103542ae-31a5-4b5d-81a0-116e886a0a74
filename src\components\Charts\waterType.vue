<template>
  <div>
    <div
      :id="id"
      :class="className"
      :style="{height:height,width:width}"
    />
  </div>
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

export default {
  name: 'WaterType',
  components: {

  },
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    propData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
  },
  watch: {
    propData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  created() {

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const datas = this.propData
      const datax = datas.map((item) => item.x)
      const datay = datas.map((item) => {
        if (item.y === 'Ⅰ') {
          return 1
        }
        if (item.y === 'Ⅱ') {
          return 2
        }
        if (item.y === 'Ⅲ') {
          return 3
        }
        if (item.y === 'Ⅳ') {
          return 4
        }
        if (item.y === 'Ⅴ') {
          return 5
        }
        return 0
      })
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption({
        grid: {
          top: '8%',
          left: '1%',
          right: '1%',
          bottom: '8%',
          containLabel: true
        },
        legend: {
          data: ['销量1'],
          show: false
        },
        tooltip: {
          trigger: 'axis',
          show: true,
          formatter(params) {
            const index = params[0].dataIndex
            const typelist = ['-', 'Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'V类']
            const res1 = `<div>统计时间: ${datas[index].localDate || '--'}</div>
                          <div>水质类别：${typelist[params[0].value] || '--'}</div>
                          <div>氨氮：<span>${datas[index].nh3n || '--'}mg/L</span> <span>${datas[index].nh3nWpi || '--'}类</span></div>
                          <div>总磷：<span>${datas[index].totalPhosphorus || '--'}mg/L</span> <span>${datas[index].totalPhosphorusWpi || '--'}类</span></div>
                          <div>PH值：<span>${datas[index].ph || '--'}</span> <span>${datas[index].phLevel || '--'}</span></div>
                          <div>溶解氧：<span>${datas[index].dissoLvedOxygen || '--'}mg/L</span> <span>${datas[index].dissoLvedOxygenWpi || '--'}类</span></div>
                          <div>高锰酸盐：<span>${datas[index].codmn || '--'}mg/L</span> <span>${datas[index].codmnWpi || '--'}类</span></div>
            `
            return res1
          }
        },
        xAxis: {
          data: datax,
          linestyle: {
            color: '#ccc'
          },
          axisLine: { // 坐标轴轴线相关设置。数学上的x轴
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: { // 坐标轴刻度标签的相关设置
            textStyle: {
              color: '#ccc',
              margin: 15
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: ['#ddd', '#ddd', '#ddd', '#ff4949', '#ddd', '#ddd']
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          min: 0,
          max: 5,
          axisLabel: {
            color(e) {
              return '#ccc'
            },
            formatter(value) {
              const texts = []
              if (value === 0 || value === '0') {
                texts.push('')
              } else if (value <= 1) {
                texts.push('I类')
              } else if (value <= 2) {
                texts.push('II类')
              } else if (value <= 3) {
                texts.push('III类')
              } else if (value <= 4) {
                texts.push('IV类')
              } else {
                texts.push('V类')
              }
              return texts
            }
          }
        },
        visualMap: {
          show: false,
          pieces: [{
            gt: 0,
            lte: 3,
            color: '#35B5AE'
          }, {
            gt: 3,
            color: '#ED573A'
          }]
        },
        series: [{
          name: '类别',
          showSymbol: false,
          smooth: true,
          abel: {
            show: true,
            position: 'top',
            textStyle: {
              color: '#fff'
            }
          },
          itemStyle: {
            normal: {
              color: '#35B5AE'

            }
          },
          areaStyle: { // 区域填充样式
            normal: {
              // 线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(53, 181, 174, 0.1)'
              },
              {
                offset: 1,
                color: 'rgba(53, 181, 174, 0)'
              }
              ], false),
              shadowColor: 'rgba(53, 181, 174, 0.1)', // 阴影颜色
              shadowBlur: 20 // shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
            }
          },
          type: 'line',
          data: datay
        }
        ]
      })
      this.chart.hideLoading()
    }
  }
}
</script>

<style scoped lang="scss">

</style>
