<template>
  <div>
    <div
      :id="id"
      :class="className"
      :style="{height:height,width:width}"
    />
  </div>
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

export default {
  name: 'Watertrendmon',
  components: {

  },
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    propData: {
      type: Array,
      default: () => []
    },
    typeName: {
      type: String,
      default: '氨氮'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {

  },
  watch: {

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const { typeName } = this
      const datax = this.propData.map((item) => item.time.substr(0, 5))
      const datay = this.propData.map((item) => ({
        value: item.value,
        symbolSize: 5,
        symbol: item.overflow ? 'circle' : 'emptyCircle',
        itemStyle: {
          normal: {
            color: item.overflow ? '#F13E3E ' : '#F0F1F5', // 拐点颜色
            borderColor: '#1890ff',
            borderWidth: item.overflow ? 0 : 1
          }
        }
      }))
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption({
        grid: {
          top: '2%',
          left: '2%',
          right: '0%',
          bottom: '2%',
          containLabel: true
        },
        legend: {
          show: false
        },
        tooltip: {
          // trigger: 'axis'
          trigger: 'axis',
          show: true,
          formatter(params) {
            const res1 = `<div>监测时间: ${params[0].axisValue}</div>
                          <div>监测指标：${typeName}</div>
            `
            let res2 = ''
            params.forEach((item) => {
              res2 += `
                <div class="paramsrow">
                  <div>${item.seriesName}: ${item.value}${typeName === 'PH值' ? '' : 'mg/L'}<div>
                <div>
              `
            })
            return res1 + res2
          }

        },
        xAxis: {
          show: true,
          data: datax,
          linestyle: {
            color: '#F0F1F5'
          },
          axisLine: { // 坐标轴轴线相关设置。数学上的x轴
            show: true,
            lineStyle: {
              color: '#F0F1F5'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F0F1F5'
            }
          },
          axisLabel: {
            show: false,
            color: '#000'
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          show: true,
          name: '单位：mg/L',
          axisLine: { // 坐标轴轴线相关设置。数学上的x轴
            show: true,
            lineStyle: {
              color: '#F0F1F5'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F0F1F5'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        series: [{
          name: '类别',
          showSymbol: true,
          abel: {
            show: true,
            position: 'top',
            textStyle: {
              color: '#fff'
            }
          },
          itemStyle: {
            normal: {
              color: '#1890ff'

            }
          },
          lineStyle: {
            normal: {
              color: '#1890ff' // 线条颜色
            }
          },
          type: 'line',
          data: datay
        }
        ]
      })
      this.chart.hideLoading()
    }
  }
}
</script>

<style scoped lang="scss">

</style>
