<template>
  <div class="map-container">
    <div id="map" :style="{ height: height }" />
    <el-input
      v-model="keywords"
      placeholder="请输入内容"
      class="input-with-select"
    >
      <el-button slot="append" icon="el-icon-search" @click="search()" />
    </el-input>
  </div>
</template>
<script>
// eslint-disable-next-line import/no-unresolved
import AMap from "AMap";
import icons from "@/assets/images/mark_bs.png";

export default {
  props: {
    height: {
      type: String,
      default: "400px"
    },
    address: {
      type: String,
      default: ""
    },
    latlng: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      map: null,
      keywords: "",
      overlays: [],
      mouseTool: null,
      geocoder: null
    };
  },
  mounted() {
    this.initMap();
  },
  methods: {
    /**
     * @method initMap
     * @description 初始化地图
     */
    initMap() {
      this.keywords = this.address;
      this.map = new AMap.Map("map", {
        resizeEnable: true,
        zoom: 12
        // mapStyle: 'amap://styles/light', // 设置地图的显示样式
      });
      this.mouseTool = new AMap.MouseTool(this.map);
      this.geocoder = new AMap.Geocoder({
        city: "全国"
      });
      const that = this;
      this.map.on("click", e => {
        that.$nextTick(() => {
          const lng = e.lnglat.getLng();
          const lat = e.lnglat.getLat();
          that.latlng.lng = lng;
          that.latlng.lat = lat;

          // console.log(that.latlng);
          that.map.clearMap();
          const marker = new AMap.Marker({
            position: [Number(lng), Number(lat)]
          });
          marker.setMap(that.map);

          AMap.plugin("AMap.Geocoder", () => {
            const geocoder = new AMap.Geocoder();
            geocoder.getAddress([lng, lat], (status, result) => {
              // console.log(result);
              if (status === "complete" && result.regeocode.formattedAddress) {
                that.keywords = result.regeocode.formattedAddress;
                // that.keywords = that.address
              } else {
                that.$message.error("您选择的地址没有解析到结果");
              }
            });
          });
        });
      });

      this.drawMaker();
      if (this.latlng.lat) {
        this.addMaker(this.latlng.lng, this.latlng.lat);
        AMap.plugin("AMap.Geocoder", () => {
          const geocoder = new AMap.Geocoder();
          geocoder.getAddress(
            [this.latlng.lng, this.latlng.lat],
            (status, result) => {
              if (status === "complete" && result.regeocode.formattedAddress) {
                // this.address = result.regeocode.formattedAddress
                this.keywords = result.regeocode.formattedAddress;
              } else {
                this.$message.error("您选择的地址没有解析到结果");
              }
            }
          );
        });
      } else if (this.address) {
        this.search(this.address);
      }
    },
    /**
     * @method search
     * @description 搜索
     */
    search(address) {
      const geocoder = new AMap.Geocoder({
        city: "全国" // 城市设为北京，默认：“全国”
      });
      this.map.clearMap();

      const that = this;
      AMap.plugin("AMap.PlaceSearch", () => {
        const autoOptions = {
          city: "全国"
        };
        const placeSearch = new AMap.PlaceSearch(autoOptions);
        // console.log(address);
        placeSearch.search(address || that.keywords, (status, result) => {
          // 搜索成功时，result即是对应的匹配数据
          if (status === "complete" && result.poiList.pois.length) {
            // console.log(address);
            const lnglat = result.poiList.pois[0].location;
            that.$emit(
              "address",
              result.poiList.pois[0].address || result.poiList.pois[0].name,
              lnglat
            );
            that.addMaker(lnglat.lng, lnglat.lat);
          } else {
            geocoder.getLocation("成都万开科技", (status1, result1) => {
              if (status1 === "complete" && result1.geocodes.length) {
                const lnglat = result1.geocodes[0].location;
                that.$emit(
                  "address",
                  result1.geocodes[0].formattedAddress,
                  that.latlng
                );
                that.addMaker(lnglat.lng, lnglat.lat);
              } else {
                that.$message.warning("根据地址查询位置失败");
              }
            });
          }
        });
      });
    },
    /**
     * @method addMaker
     * @description 添加marker
     */
    addMaker(lng, lat) {
      const icon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(19, 33),
        // 图标的取图地址
        image: icons,
        // 图标所用图片大小
        imageSize: new AMap.Size(19, 33)
        // 图标取图偏移量
        // imageOffset: new AMap.Pixel(-9, -3)
      });
      this.marker = new AMap.Marker({
        icon,
        position: [Number(lng), Number(lat)]
      });
      if (this.overlays.length) {
        this.map.remove(this.overlays[0].obj);
        this.overlays.shift();
      }
      this.overlays.push({ obj: this.marker, position: { lng, lat } });
      this.submitMap();
      this.marker.setMap(this.map);
      this.map.setCenter(new AMap.LngLat(lng, lat));
    },
    /**
     * @method drawMaker
     * @description 地图鼠标点击绘制图标
     */
    drawMaker() {
      // 监听draw事件可获取画好的覆盖物
      if (this.marker) {
        this.overlays.push({
          obj: this.marker,
          position: { lng: this.lng, lat: this.lat }
        });
      }
      // this.overlays = [];
      const that = this;
      this.mouseTool.on("draw", e => {
        const position = e.obj.getPosition();
        if (that.overlays.length) {
          that.map.remove(that.overlays[0].obj);
          that.overlays.shift();
        }
        that.overlays.push({ obj: e.obj, position });
        that.submitMap();
        that.geocoder.getAddress(position, (status, result) => {
          if (status === "complete" && result.info === "OK") {
            that.$emit(
              "address",
              result.regeocode.formattedAddress,
              that.latlng
            );
          }
        });
      });
      this.mouseTool.marker({});
    },
    submitMap() {
      this.$emit("submit", this.overlays);
    }
  }
};
</script>
<style lang="scss" scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  #map {
    width: 100%;
  }
  .input-with-select {
    width: 70%;
    height: 42px;
    position: absolute;
    top: 50px;

    ::v-deep .el-input__inner {
      height: 42px;
    }
  }
}
</style>
<style>
.amap-logo {
  display: none !important;
}
.amap-copyright {
  opacity: 0;
}
</style>
