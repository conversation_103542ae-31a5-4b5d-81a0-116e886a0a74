<template>
  <div class="map-container">
    <div
      id="map"
      :style="{height:height }"
    />
  </div>
</template>
<script>
// eslint-disable-next-line import/no-unresolved
import AMap from 'AMap'
// eslint-disable-next-line import/no-unresolved
import icons from '@/assets/images/mark_bs.png'

export default {
  props: {
    height: {
      type: String,
      default: '400px'
    },
    address: {
      type: String,
      default: ''
    },
    detailShow: {
      type: Boolean,
      default: false
    },
    latlng: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      map: null,
      keywords: '',
      overlays: [],
      mouseTool: null,
      geocoder: null
    }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    /**
     * @method initMap
     * @description 初始化地图
     */
    initMap() {
      this.map = new AMap.Map('map', {
        resizeEnable: true,
        zoom: 14,
        // mapStyle: 'amap://styles/light', // 设置地图的显示样式
        center: [104.071216, 30.668516]
      })
      if (!this.latlng.lat) {
        this.mouseTool = new AMap.MouseTool(this.map)
        this.geocoder = new AMap.Geocoder({
          city: '全国'
        })
        this.drawMaker()
      }
      if (this.latlng.lat) {
        this.addMaker(this.latlng.lng, this.latlng.lat)
      } else if (this.address) {
        this.search(this.address)
      }
    },
    /**
     * @method search
     * @description 搜索
     */
    /**
     * @method addMaker
     * @description 添加marker
     */
    addMaker(lng, lat) {
      const icon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(19, 33),
        // 图标的取图地址
        image: icons,
        // 图标所用图片大小
        imageSize: new AMap.Size(19, 33)
        // 图标取图偏移量
        // imageOffset: new AMap.Pixel(-9, -3)
      })
      this.marker = new AMap.Marker({
        icon,
        position: [Number(lng), Number(lat)]
      })
      if (this.overlays.length) {
        this.map.remove(this.overlays[0].obj)
        this.overlays.shift()
      }
      this.overlays.push({ obj: this.marker, position: { lng, lat }})
      this.submitMap()
      this.marker.setMap(this.map)
      this.map.setCenter(new AMap.LngLat(lng, lat))
    },
    /**
     * @method drawMaker
     * @description 地图鼠标点击绘制图标
     */
    submitMap() {
      this.$emit('submit', this.overlays)
    }
  }
}
</script>
<style lang="scss" scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  #map {
    width: 100%;
  }
  .input-with-select {
    width: 60%;
    position: absolute;
    top: 50px;
    left: 20%;
  }
}
</style>
