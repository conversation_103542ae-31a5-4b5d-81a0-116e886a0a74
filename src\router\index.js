 /* eslint-disable */
import Vue from "vue";
import Router from "vue-router";
import { home } from "@/router/modules/home"; // 我的首页
// import { enterpriseManagement } from '@/router/modules/enterpriseManagement' // 一企一档
// import { alarmManagement } from '@/router/modules/alarmManagement' // 告警中心
import { alarmCenter } from "@/router/modules/alarmCenter"; // 告警中心
import { thresholdSettings } from "@/router/modules/thresholdSettings"; // 阈值设置
import { airMonitor } from "@/router/modules/airMonitor"; // 空气监测
import { outletMonitoring } from "@/router/modules/outletMonitoring"; // 排口监测
import { waterManagement } from "@/router/modules/waterManagement"; // 水质检测
import { heavyPollution } from "@/router/modules/heavyPollution"; // 重污监测
import { camera } from "@/router/modules/camera"; // 视频监控
import { taskManagement } from "@/router/modules/taskManagement"; // 任务调度
import { emergencyManagement } from "@/router/modules/emergencyManagement"; // 部门联动
import { commandManagement } from "@/router/modules/commandManagement"; // 应急指挥
import { patrolManagement } from "@/router/modules/patrolManagement"; // 巡查管理
import { vehicleManagement } from "@/router/modules/vehicleManagement"; // 车辆管理
import { reportManagement } from "@/router/modules/reportManagement"; // 报表管理
import { rubbishSortManagement } from "@/router/modules/rubbishSortManagement"; // 垃圾分类
import { messageManagement } from "@/router/modules/messageManagement"; // 通知公告
import { systemManagement } from "@/router/modules/systemManagement"; // 系统管理
import { smartBriefing } from "@/router/modules/smartBriefing"; // 智能简报
// import { equipManagement } from '@/router/modules/equipManagement' // 设备管理
import { personalCenter } from "@/router/modules/personalCenter"; // 个人中心
import { processConfiguration } from "@/router/modules/processConfiguration"; // 配置管理
// import { noticeManagement } from '@/router/modules/noticeManagement' // 消息中心
import { errorPage_404 } from "@/router/modules/404";
import { waterSourceMonitoring } from "@/router/modules/waterSourceMonitoring"; // 水源监测
import { waterAmount } from "@/router/modules/waterAmount"; // 水量监测
import { riskControl } from "@/router/modules/riskPreventionControl.js"; // 风险防控
import { dailyTasks } from "@/router/modules/dailyTasks"; // 日常任务
import { dispatchEmer } from "@/router/modules/dispatchEmer"; // 应急任务
import { resourceManagement } from "@/router/modules/resourceManagement"; // 资源管理
import { newsManagement } from "@/router/modules/newsManagement"; // 资讯管理
import { deviceManagement } from "@/router/modules/deviceManagement"; // 设备管理
import { rightsManagement } from "@/router/modules/rightsManagement"; // 权限管理
import { patrolVideoLog } from "@/router/modules/patrolVideoLog"; // 寻阅日志
import { health } from "@/router/modules/health"; // 环境健康
import { workbench } from "@/router/modules/workbench"; // 工作台
import { taskTrack } from "@/router/modules/taskTrack"; // 工作台
import { pollutionSourceSurveyChecklist } from "@/router/modules/pollutionSourceSurveyChecklist";
/* Layout */
import Layout from "@/layout";
import { carbonManagement } from "@/router/modules/carbonManagement"; // 活性炭管理
// 404Page
Vue.use(Router);

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 * 固定路由
 */
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path*",
        component: () => import("@/views/redirect/index")
      }
    ]
  },
  {
    path: "/auth-redirect",
    component: () => import("@/views/login/auth-redirect"),
    hidden: true
  },
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404"),
    hidden: true
  },
  {
    path: "/401",
    component: () => import("@/views/error-page/401"),
    hidden: true
  }
];
/**
 * asyncRoutes
 * 动态路由
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  home,
  workbench,
  taskTrack,
  pollutionSourceSurveyChecklist,
  airMonitor,
  carbonManagement,
  waterManagement,
  waterSourceMonitoring,
  waterAmount,
  outletMonitoring,
  heavyPollution,
  riskControl,
  camera,
  emergencyManagement,
  // taskManagement,
  dailyTasks,
  patrolManagement,
  dispatchEmer,
  // commandManagement,
  smartBriefing,
  resourceManagement,
  newsManagement,
  messageManagement,
  reportManagement,
  alarmCenter,
  thresholdSettings,
  processConfiguration,
  deviceManagement,
  vehicleManagement,
  rubbishSortManagement,
  rightsManagement,
  systemManagement,
  personalCenter,
  patrolVideoLog,
  health,
  errorPage_404
];
const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({
      y: 0
    }),
    routes: constantRoutes
  });

const router = createRouter();

export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
