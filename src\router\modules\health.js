/* eslint-disable */
/** When your routing table is too long, you can split it into small modules**/

import Layout from '@/layout'

export const health = {
  path: '/health',
  component: Layout,
  redirect: '/river-info',
  alwaysShow: true,
  meta: {
    title: '环境健康',
    icon: 'water-management',
    roles: ['environment_health']
  },
  children: [
    {
      path: '/firm-manage',
      name: 'FirmManage',
      component: () => import('@/views/health/firmManage/firmManage.vue'),
      meta: {
        title: '污染源企业管理',
        // icon: 'water-site',
        roles: ['river_info_manager']
      }
    },

    {
      path: '/firm-manage/:id',
      name: 'FirmManageDetail',
      component: () => import('@/views/health/firmManage/firmManageDetail.vue'),
      meta: {
        title: '污染源企业管理详情',
      }
    },


    {
      path: '/river-info',
      name: 'river-info',
      // component: () => import('@/views/equipManagement/waterSite'),
      component: () => import('@/views/health/riverInfo'),
      meta: {
        title: '河流信息管理',
        // icon: 'water-site',
        roles: ['river_info_manager']
      }
    },
    {
      path: '/warter-env-data',
      name: 'warter-env-data',
      // component: () => import('@/views/equipManagement/waterSite'),
      component: () => import('@/views/health/warterEnvData'),
      meta: {
        title: '水环境数据',
        // icon: 'water-site',
        roles: ['water_environment']
      }
    },
  ]
}
