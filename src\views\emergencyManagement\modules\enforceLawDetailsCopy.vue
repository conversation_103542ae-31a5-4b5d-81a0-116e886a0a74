<style lang="scss">
body {
  background-color: #ebf2ee;
}

.customConfirm {
  .el-message-box__content {
    color: red;
  }
}
</style>

<style lang="scss" scoped>
@import "@/styles/mixin.scss";

.text-bold {
  font-size: 18px;
  font-weight: bold;
}

.title-content {
  font-size: 25px;
  font-weight: bolder;
}

.infor-normal {
  font-size: 16px;
  font-weight: none;
}

.feedback {
  .feedback-content {
    margin-bottom: 40px;

    &:last-child {
      margin-bottom: 0;
    }

    .feedback-txt {
      padding-left: 20px;

      span {
        &:nth-child(1) {
          display: inline-block;
          width: 80px;
          color: #202020;
          text-align: left;
        }

        &:nth-child(2) {
          color: #333333;
        }
      }
    }
  }
}

.law-container {
  display: flex;
  flex-direction: row;
  min-width: 1000px;
  padding: 20px;
  margin-bottom: 15px;

  .left {
    flex: 6 2 auto;
    margin-right: 10px;

    .button-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 0;

      .rights {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 88px;
        height: 32px;
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 400;
        color: #4d4d4d;
        cursor: pointer;
        border: 1px solid rgba(153, 153, 153, 0.2);
        // opacity: 0.2;
        border-radius: 4px;

        span {
          margin-left: 5px;
        }
      }
    }

    .message-title-bold {
      font-size: 18px;
      font-weight: bold;
    }

    .message-title-normal {
      font-size: 16px;

      /* white-space: nowrap; */
    }

    .message-describe {
      font-size: 16px;
    }

    .message-journal {
      font-size: 16px;

      span {
        margin: 10px 0;
        vertical-align: middle;
      }

      span:nth-of-type(2) {
        margin: 0 20px;
      }
    }
  }

  .right {
    flex: 1 0 auto;

    .message-title-bold {
      font-size: 18px;
      font-weight: bold;
    }

    .message-title-normal {
      font-size: 16px;
      white-space: nowrap;
    }

    .message-describe {
      font-size: 16px;
    }

    .message-detail {
      color: blue;
      text-decoration: underline;
      cursor: pointer;
    }
  }
}

.card-box {
  @include card-container;
}

.attachment-content {
  img {
    display: inline-block;
    width: 64px;
    height: 64px;
  }

  a {
    @include textEllipsis;

    display: inline-block;
    width: 100%;
    font-size: 14px;
  }
}

.task-image {
  .el-image {
    display: inline-block;
    width: 150px;
    height: 150px;
    margin: 0 10px 10px 0;
  }

  video {
    display: inline-block;
    height: 150px;
    // width: 150px;
    margin: 0 10px 10px 0;
  }

  .video {
    background: linear-gradient(
      top,
      rgba(0, 0, 0, 0),
      rgba(0, 0, 0, 1),
      rgba(0, 0, 0, 1)
    );
  }
}

.alarm-info {
  margin-bottom: 15px;
  font-size: 16px;
  color: #212121;
  // span:last-child {
  //   margin-left: 15px;
  // }
}

.node-item {
  display: inline-flex;
  align-items: center;
  margin-right: 1%;
  margin-bottom: 10px;
  color: #666666;
}

::v-deep .flow-chart-box {
  .el-collapse-item__content {
    padding-bottom: 100px !important;
  }
}

.file_upload {
  display: flex;
}
</style>
<template>
  <section v-loading="taskDetailLoading" class="law-container">
    <el-card class="left card-box" shadow="never" style="margin-bottom: 20px;">
      <h3>{{ eventDetail.title ? eventDetail.title : taskDetail.title }}</h3>
      <div class="button-list">
        <div class="lefts">
          <el-button
            type="info"
            plain
            size="small"
            icon="el-icon-back"
            @click="returnBack()"
            >返回</el-button
          >
          <el-button
            v-if="taskDetail.completeStatus == 0 && btnList.includes(210)"
            type="success"
            @click="handleComplted"
            >完成任务</el-button
          >
          <el-button
            v-if="btnList.includes(145)"
            type="success"
            :disabled="taskDetail.isPush"
            @click="handleClickTaskPush"
            >{{ !taskDetail.isPush ? "任务上报" : "任务已上报" }}</el-button
          >
        </div>
        <div class="rights" @click="Refresh">
          <img src="@/assets/images/<EMAIL>" alt="" />
          <span>刷新</span>
        </div>
        <!-- <el-button
          v-if="taskDetail.eventId === null"
          type="success"
          :disabled="taskDetail.completeStatus ==1 || taskDetail.myTaskType ==3"
          @click="handleCreateTask(taskDetail)"
        >手动生成</el-button> -->
        <!-- <el-button
          v-if="taskDetail.eventId === null"
          type="primary"
          :disabled="taskDetail.completeStatus ==1 || taskDetail.myTaskType ==3"
          @click="connectPlan(taskDetail)"
        >生成调度任务</el-button>
        <el-button
          v-if="taskDetail.eventId !== null"
          type="success"
          @click="connectTask(taskDetail)"
        >关联任务</el-button> -->
        <!-- <el-button
          type="success"
          @click="complateDialogVisibled = true"
        >完成任务</el-button> -->
      </div>
      <el-collapse v-model="activeLeftNames">
        <!-- <el-collapse-item name="1">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">类型</div>
          </template>
<div class="message-title-normal textIndent">
  {{ $route.query.alarmType }}
  {{ Number(taskDetail.eventTypeId)
  ? alarmTypes[Number(taskDetail.eventTypeId) - 1].label : '' }}</div>
</el-collapse-item> -->
        <el-collapse-item v-if="searchListData.length" name="2">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">
              告警信息
            </div>
          </template>
          <div class="alarm-info" style="display: flex;">
            <div>
              <span style="margin-right: 20px;"
                >站点：
                {{
                  searchListData.length ? searchListData[0].stationName : "--"
                }}
              </span>
              <span style="margin-right: 20px;"
                >类型：
                <!-- {{ handleType(searchListData[0].stationTypeId) }} -->
                {{ eventDetail.stationTypeName }}
                {{ searchListData[0].stationScopeTypeId ? "/" : "" }}
                {{
                  searchListData[0].stationScopeTypeId
                    ? stationType[searchListData[0].stationScopeTypeId]
                    : ""
                }}
              </span>
            </div>
            <!-- <span @click="handleClickMap">
              地址：{{ searchListData.length ? searchListData[0].stationAddress : '--' }}<img
                src="@/assets/images/position.png"
                alt=""
                style="width: 20px;cursor: pointer;"
              ></span> -->
            <div>
              <el-button
                v-if="historicalList.length !== 0"
                type="success"
                style="margin-left: 20px;"
                @click="handleHistorical(taskDetail.taskId)"
                >告警历史</el-button
              >
              <el-button
                v-if="
                  historicalList.length !== 0 &&
                    taskDetail.stationTypeId !== 10 &&
                    eventDetail.stationTypeId != 16 &&
                    eventDetail.stationTypeId != 17 &&
                    !(
                      eventDetail.stationTypeId === 11 &&
                      eventDetail.taskAlarmList &&
                      eventDetail.taskAlarmList[0] &&
                      eventDetail.taskAlarmList[0].type === 2
                    )
                "
                type="primary"
                style="margin-left: 20px;"
                @click="handleHistory(taskDetail)"
                >历史监测</el-button
              >

              <el-button
                v-if="[16, 17].includes(eventDetail.stationTypeId) ||
                (
                  eventDetail.stationTypeId === 11 &&
                  eventDetail.taskAlarmList &&
                  eventDetail.taskAlarmList[0] &&
                  eventDetail.taskAlarmList[0].type === 2
                )
                "
                type="primary"
                style="margin-left: 20px;"
                @click="checkMonitor(taskDetail)"
                >查看监控</el-button
              >
            </div>
          </div>
          <el-table
            ref="alarmTable"
            :data="searchListData"
            :header-cell-style="{ background: '#F7FCFB' }"
            class="alarm-table"
            border
          >
            <el-table-column
              label="告警内容"
              align="center"
              prop="alarmContent"
              min-width="200"
            />
            <el-table-column
              label="告警项"
              align="center"
              prop="alarmItemName"
            />
            <el-table-column
              v-if="
                eventDetail.stationTypeId === 17 ||
                  (eventDetail.stationTypeId === 11 &&
                    eventDetail.taskAlarmList &&
                    eventDetail.taskAlarmList[0] &&
                    eventDetail.taskAlarmList[0].type === 2)
              "
              label="置信度"
              align="center"
              prop="confidence"
            />

            <el-table-column
              v-if="
                ![16, 17].includes(eventDetail.stationTypeId) &&
                  !(
                    eventDetail.stationTypeId === 11 &&
                    eventDetail.taskAlarmList &&
                    eventDetail.taskAlarmList[0] &&
                    eventDetail.taskAlarmList[0].type === 2
                  )
              "
              label="告警值"
              align="center"
              prop="alarmValue"
            >
              <template slot-scope="scope">
                {{ scope.row.alarmValue
                }}{{ scope.row.alarmItemUnit }}</template
              >
            </el-table-column>
            <el-table-column
              v-if="
                ![16, 17].includes(eventDetail.stationTypeId) &&
                  !(
                    eventDetail.stationTypeId === 11 &&
                    eventDetail.taskAlarmList &&
                    eventDetail.taskAlarmList[0] &&
                    eventDetail.taskAlarmList[0].type === 2
                  )
              "
              label="告警阈值"
              align="center"
              prop="alarmThreshold"
            >
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.alarmValueType === 0 ||
                      scope.row.alarmValueType === null
                  "
                >
                  {{ scope.row.alarmThreshold
                  }}{{ scope.row.alarmItemUnit }}</span
                >
                <span v-if="scope.row.alarmValueType === 1">
                  {{ scope.row.alarmMinValue
                  }}{{ scope.row.alarmItemUnit }}&lt;,&lt;
                  {{ scope.row.alarmMaxValue
                  }}{{ scope.row.alarmItemUnit }}</span
                >
                <span v-if="scope.row.alarmValueType === 2"
                  >&lt; {{ scope.row.alarmMinValue
                  }}{{ scope.row.alarmItemUnit }},&gt;
                  {{ scope.row.alarmMaxValue
                  }}{{ scope.alarmItemUnit.unit }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="告警时间"
              align="center"
              width="250px"
              prop="alarmTime"
            >
              <template v-slot="{ row }">
                <div>
                  {{
                    row.alarmTime
                      ? eventDetail.stationTypeName === "环境噪声站点"
                        ? row.alarmTime.substring(0, 10)
                        : row.alarmTime.substring(0, 16)
                      : ""
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="
                [16, 17].includes(eventDetail.stationTypeId) ||
                  (eventDetail.stationTypeId === 11 &&
                    eventDetail.taskAlarmList &&
                    eventDetail.taskAlarmList[0] &&
                    eventDetail.taskAlarmList[0].type === 2)
              "
              label="操作"
              align="center"
              width="250px"
            >
              <template v-slot="{ row }">
                <el-button type="text" @click="detail(row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
        <el-collapse-item
          v-if="taskDetail && taskDetail.reasonAnalysis"
          name="3"
        >
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">
              成因分析
            </div>
          </template>
          <div v-html="taskDetail.reasonAnalysis.replace(/\n/g, '<br/>')" />
          <!-- <div
            v-if="picList !== undefined"
            class="message-title-normal textIndent"
          >{{ eventDetail.content }}</div>
          <div
            v-else
            class="message-title-normal textIndent"
          >{{ taskDetail.content }}</div> -->
        </el-collapse-item>
        <!-- 地址 -->
        <el-collapse-item v-if="picList !== undefined" name="8">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">地址</div>
          </template>
          <div style="display: flex;">
            <div v-if="searchListData.length">
              <span style="margin-right: 20px;"
                >街道：
                {{
                  searchListData.length ? searchListData[0].streetName : "--"
                }}
              </span>
            </div>
            <div
              class="message-title-normal textIndent"
              style="cursor: pointer;"
              @click="handleClickMaps"
            >
              {{ eventDetail.address }}
              <img
                src="@/assets/images/position.png"
                alt=""
                style="width: 20px; cursor: pointer;"
              />
            </div>
          </div>
          <!-- <div class="message-title-normal textIndent">{{ eventDetail.address }}</div> -->
        </el-collapse-item>
        <el-collapse-item name="4">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">附件</div>
          </template>
          <div v-if="picList !== undefined">
            <el-image
              v-for="(item, idx) in picList"
              :key="idx"
              style="width: 100px; height: 100px; margin-right: 10px;"
              :preview-src-list="[
                `${item.annexUrl}?time=${new Date().getTime()}`
              ]"
              :src="`${item.annexUrl}?time=${new Date().getTime()}`"
              fit="cover"
            />
          </div>
          <div v-else>
            <div v-if="fileLists.length > 0" class="message-attachment">
              <div
                v-for="(file, index) in fileLists"
                :key="index"
                class="attachment-content"
              >
                <img
                  v-if="file.type === 1"
                  src="@/assets/local_images/word.png"
                />
                <img
                  v-if="file.type === 2"
                  src="@/assets/local_images/excel.png"
                />
                <img
                  v-if="file.type === 3"
                  src="@/assets/local_images/pdf.png"
                />
                <img
                  v-if="file.type === 4"
                  src="@/assets/local_images/ppt.png"
                />
                <img
                  v-if="file.type === 5"
                  src="@/assets/local_images/zip.png"
                />
                <a :href="file.url" target="_blank" :download="file.fileName">{{
                  file.fileName
                }}</a>
              </div>
            </div>
            <div v-else>暂无附件</div>
          </div>
        </el-collapse-item>
        <el-collapse-item name="5" class="flow-chart-box">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">
              处理流程
            </div>
          </template>
          <flow-chart
            ref="flowChart"
            flow-chart-id="flowChart2"
            :task-id="taskId"
            :user-id="userId"
            :model-data="flowChartData"
            @complete="handleComplated"
            @detail="handleDetail"
            @add="handleAddNode"
            @close="handleNodeClose"
            @params="handleParams"
          />
        </el-collapse-item>
        <el-collapse-item name="8">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">
              执行节点
            </div>
          </template>
          <div class="node_list">
            <div
              v-for="(item, indexc) in eventDetail.myTaskNodeList"
              :key="indexc"
              class="node-item"
            >
              <img
                v-if="item.completeStatus !== 1"
                src="@/assets/images/<EMAIL>"
                alt=""
                style="width: 16px; height: 16px; margin-right: 16px;"
              />
              <img
                v-else
                src="@/assets/images/<EMAIL>"
                alt=""
                style="width: 16px; height: 16px; margin-right: 16px;"
              />
              <span style="margin: 0 20px 0 8px;">
                {{ item.departmentName }} - {{ item.userName }}：
                {{ item.taskNodeContent }}</span
              >
              <el-button
                v-if="item.completeStatus == 0"
                type="primary"
                @click="opencompleted(item)"
                >待办</el-button
              >
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item name="6">
          <template slot="title">
            <div class="text-bold text-indent">执行反馈</div>
          </template>
          <el-timeline
            v-if="executiveFeedbackList.length"
            class="feedback feedbacks"
          >
            <el-timeline-item
              v-for="(item, indexa) in executiveFeedbackList"
              :key="indexa"
              :timestamp="parseTime(item.time)"
              placement="top"
            >
              <el-card shadow="never" style="width: 100%;">
                <el-row
                  slot="header"
                  :gutter="10"
                  type="flex"
                  align="middle"
                  justify="start"
                >
                  <el-col :span="1.5">
                    <el-avatar
                      :src="item.avatarUrl"
                      icon="el-icon-user-solid"
                    />
                  </el-col>
                  <el-col :span="20" style="color: #666666;">
                    <span>{{ item.accountName }}</span>
                    <span>{{ item.content }}</span>
                  </el-col>
                  <el-col>
                    <!-- <el-button
                      type="success"
                      style="color: #3BB66F;background: #fff;"
                    >查看图片</el-button> -->
                  </el-col>
                  <el-col :span="8" style="color: #666666;">
                    <span>{{
                      item.time
                        ? item.time.substr(0, item.time.length - 3)
                        : "--"
                    }}</span>
                  </el-col>
                </el-row>
                <div class="task-content">
                  <div class="task-describe">
                    <div
                      v-if="
                        item.logTypeId !== 3 &&
                          item.logTypeId !== 5 &&
                          item.logTypeId !== 7 &&
                          item.logTypeId !== 8 &&
                          item.logTypeId !== 15 &&
                          item.logTypeId !== 16 &&
                          eventDetail.stationTypeId === 15
                      "
                    >
                      巡检巡查情况：
                    </div>
                    {{ item.remark }}
                    <div class="task-image">
                      <template v-for="(pic, indexs) in item.taskAnnexList">
                        <ComImage
                          :key="indexs"
                          :src="pic.annexUrl"
                          fit="cover"
                          :preview-src-list="[
                            ...item.taskAnnexList.map(it => it.annexUrl)
                          ]"
                        />
                      </template>
                    </div>
                  </div>
                  <div
                    v-if="
                      item.logTypeId !== 3 &&
                        item.logTypeId !== 2 &&
                        item.logTypeId !== 5 &&
                        item.logTypeId !== 7 &&
                        item.logTypeId !== 8 &&
                        item.logTypeId !== 15 &&
                        item.logTypeId !== 16 &&
                        eventDetail.stationTypeId === 15
                    "
                  >
                    本次巡查是否存在问题：{{ item.isIssues ? "是" : "否" }}
                  </div>
                  <div
                    v-if="
                      item.isIssues &&
                        item.logTypeId !== 3 &&
                        item.logTypeId !== 2 &&
                        item.logTypeId !== 5 &&
                        item.logTypeId !== 7 &&
                        item.logTypeId !== 8 &&
                        item.logTypeId !== 15 &&
                        item.logTypeId !== 16 &&
                        eventDetail.stationTypeId === 15
                    "
                  >
                    问题类型:{{
                      item.patrolIssuesManifest.patrolIssuesManifestTypeName ||
                        ""
                    }}
                  </div>
                  <div v-if="item.isIssues && item.patrolIssuesManifest">
                    <div>需整改问题：</div>
                    <el-table
                      :data="[item.patrolIssuesManifest]"
                      style="width: 100%;"
                    >
                      <el-table-column prop="size" label="文件名称">
                        <template slot-scope="{ row }">
                          <span>{{ `${row.name}问题清单` }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作">
                        <template slot-scope="{ row }">
                          <el-button type="text" @click="previewPDF(row)"
                            >预览</el-button
                          >
                          <el-button type="text" @click="downloadPDF(row)"
                            >下载</el-button
                          >
                        </template>
                      </el-table-column>
                    </el-table>
                    <div>
                      立行整改：{{
                        item.patrolIssuesManifest.isRectification ? "是" : "否"
                      }}
                    </div>
                    <div v-if="item.patrolIssuesManifest.isRectification">
                      整改结果：{{
                        item.patrolIssuesManifest.rectificationRemark
                      }}
                    </div>
                    <div
                      v-if="
                        item.patrolIssuesManifest.isRectification &&
                          item.patrolIssuesManifest.rectificationImages.length >
                            0
                      "
                    >
                      现场整改情况：
                    </div>
                    <div
                      v-if="item.patrolIssuesManifest.isRectification"
                      class="task-image"
                    >
                      <template
                        v-for="(pic, indexs) in item.patrolIssuesManifest
                          .rectificationImages"
                      >
                        <ComImage
                          :key="indexs"
                          :src="pic.url"
                          fit="cover"
                          :preview-src-list="[
                            ...item.patrolIssuesManifest.rectificationImages.map(
                              it => it.url
                            )
                          ]"
                        />
                      </template>
                    </div>
                    <div v-if="!item.patrolIssuesManifest.isRectification">
                      整改时限：{{
                        item.patrolIssuesManifest.rectificationTimeLimit
                      }}
                    </div>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
          <div v-else>暂无反馈</div>
        </el-collapse-item>
        <el-collapse-item name="7">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">
              已读回执
            </div>
          </template>
          <div>
            <el-tabs v-model="activeName2" type="border-card">
              <el-tab-pane :label="'已读(' + read.length + ')'" name="first">
                <div class="message-journal">
                  <div
                    v-for="(item, indexb) in read"
                    :key="indexb"
                    style="display: inline-flex; align-items: center;"
                  >
                    <img
                      src="@/assets/images/<EMAIL>"
                      alt=""
                      style="width: 16px; height: 16px;"
                    />
                    <span style="margin: 0 20px 0 8px;">
                      {{ item.departmentName }} - {{ item.userName }}
                      {{ indexb + 1 === read.length ? "" : ";" }}</span
                    >
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane :label="'未读(' + unread.length + ')'" name="second">
                <div class="message-journal">
                  <div
                    v-for="(item, indexc) in unread"
                    :key="indexc"
                    style="display: inline-flex; align-items: center;"
                  >
                    <img
                      src="@/assets/images/<EMAIL>"
                      alt=""
                      style="width: 16px; height: 16px;"
                    />
                    <span style="margin: 0 20px 0 8px;">
                      {{ item.departmentName }} - {{ item.userName }}
                      {{ indexc + 1 === unread.length ? "" : ";" }}</span
                    >
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>
    <el-card class="right card-box" shadow="never" style="margin-bottom: 20px;">
      <el-collapse v-model="activeRightNames">
        <!-- <el-collapse-item name="1">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">部门</div>
          </template>
          <el-row
            :gutter="10"
            type="flex"
          >
            <el-col :span="8">
              <div class="message-title-normal textIndent">执行者：</div>
            </el-col>
            <el-col :span="16">
              <div
                class="message-detail"
                @click="handleCheckDetail"
              >查看详情</div>
            </el-col>
          </el-row>
        </el-collapse-item> -->
        <el-collapse-item v-if="taskDetail.eventTypeId !== 0" name="1">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">部门</div>
          </template>
          <el-row :gutter="10" type="flex">
            <el-col :span="8">
              <div class="message-title-normal textIndent">发起人：</div>
            </el-col>
            <el-col :span="16">
              <div style="color: #999999;">
                {{ taskDetail.departmentName }} - {{ taskDetail.userName }}
              </div>
            </el-col>
          </el-row>
        </el-collapse-item>
        <el-collapse-item name="2">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">日期</div>
          </template>
          <el-row :gutter="10" type="flex">
            <el-col :span="8">
              <div class="message-title-normal textIndent">开始时间：</div>
            </el-col>
            <el-col :span="16">
              <div
                class="message-title-normal textIndent"
                style="color: #999999;"
              >
                {{
                  taskDetail.startTime
                    ? taskDetail.startTime.substr(
                        0,
                        taskDetail.startTime.length - 3
                      )
                    : undefined
                }}
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10" type="flex">
            <el-col :span="8">
              <div class="message-title-normal textIndent">更新时间：</div>
            </el-col>
            <el-col :span="16">
              <div
                class="message-title-normal textIndent"
                style="color: #999999;"
              >
                {{
                  taskDetail.updateTime
                    ? taskDetail.updateTime.substr(
                        0,
                        taskDetail.updateTime.length - 3
                      )
                    : undefined
                }}
              </div>
            </el-col>
          </el-row>
        </el-collapse-item>
        <el-collapse-item v-if="taskDetail.updateTime" name="3">
          <template slot="title">
            <div class="message-title-bold marginRight textIndent">更新</div>
          </template>
          <div
            v-for="(item, index) in taskDetail.taskLogList"
            :key="index"
            :gutter="10"
            type="flex"
          >
            <div :span="8">
              <div
                class="message-title-normal textIndent"
                style="display: flex;
                align-items: center;
                justify-content: space-between;
                color: #999999;"
              >
                <span v-if="item.logTypeId !== 1">
                  {{ item.departmentName }} - {{ item.userName }}：</span
                >{{ item.content }}
                <span>{{
                  item.time
                    ? item.time.substr(0, item.time.length - 3)
                    : undefined
                }}</span>
              </div>
            </div>
            <!-- <el-col :span="16">
              <div class="message-title-normal textIndent">
                {{ taskDetail.updateTime ? taskDetail.updateTime
                  .substr(0,taskDetail.updateTime.length-3) : undefined }}</div>
            </el-col> -->
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>
    <el-dialog
      v-loading="finishTaskLoading"
      :visible.sync="complateDialogVisibled"
      title="完成任务"
      element-loading-text="任务完成提交中..."
      width="550px"
      :close-on-click-modal="false"
      @close="
        (complatedForm = {
          content: undefined,
          fileList: []
        }),
          (fileList = [])
      "
    >
      <el-form ref="complatedForm" :model="complatedForm" label-width="100px">
        <el-form-item
          label="完成备注"
          prop="content"
          :rules="[
            { required: true, message: '请输入完成备注', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="complatedForm.content"
            type="textarea"
            maxlength="255"
            show-word-limit
            resize="none"
            :rows="6"
            style="float: left; width: 400px;"
          />
        </el-form-item>
        <el-form-item label="附件：" prop="fileList">
          <el-upload
            ref="upload"
            class="new-task-upload1"
            :auto-upload="false"
            :http-request="noop"
            :file-list="fileList"
            :on-change="uploadFile"
            :on-preview="handleFilePreview"
            :before-remove="fileBeforeRemove"
            :on-remove="handleFileRemove"
            :multiple="true"
            action="customize"
          >
            <el-button size="medium" type style="width: 400px; color: blue;"
              >添加附件</el-button
            >
            <div slot="tip" class="el-upload__tip">只能上传image文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="complateDialogVisibled = false">取 消</el-button>
        <el-button type="primary" @click="finishTask">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-loading="finishTaskLoading"
      :visible.sync="complateDialogVisibled1"
      title="完成任务"
      element-loading-text="任务完成提交中..."
      width="60%"
      :close-on-click-modal="false"
      @close="handleCloseComplate"
    >
      <el-form
        ref="complatedForm1"
        :model="complatedForm1"
        label-width="120px"
        style="max-height: 500px; padding-right: 20px; overflow: auto;"
        @scroll.native="handelScroll"
      >
        <el-form-item
          label="巡检巡查情况"
          prop="completeRemark"
          :rules="[
            { required: true, message: '请输入巡检巡查情况', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="complatedForm1.completeRemark"
            type="textarea"
            :rows="3"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="现场情况" prop="taskAnnexList">
          <el-upload
            ref="upload"
            class="new-task-upload1"
            :auto-upload="false"
            :http-request="noop"
            :on-change="uploadFile1"
            :show-file-list="false"
            :multiple="true"
            accept="jpg,png,jpeg"
            action="customize"
          >
            <div class="file_upload">
              <el-button
                size="medium"
                type
                style="width: 200px; margin-right: 20px; color: blue;"
                >上传附件</el-button
              >
              <div slot="tip" class="el-upload__tip">支持jpg、png、jpeg</div>
            </div>
          </el-upload>
          <el-table :data="complatedForm1.taskAnnexList" style="width: 100%;">
            <el-table-column prop="name" label="文件名称" />
            <el-table-column prop="size" label="文件大小">
              <template slot-scope="{ row }">
                <span>{{ `${row.size}${row.unit}` }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="{ row }">
                <el-button type="text" @click="previewImg(row)">预览</el-button>
                <el-button type="text" @click="downloadImg(row)"
                  >下载</el-button
                >
                <el-button type="text" @click="deleteImg1(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item
          label="本次巡查是否存在问题"
          prop="isIssues"
          label-width="auto"
          :rules="[{ required: true, message: '请选择', trigger: 'change' }]"
        >
          <el-radio-group v-model="complatedForm1.isIssues">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <div
          v-if="complatedForm1.isIssues"
          style="display: flex; justify-content: space-between; margin: 30px 0 0 50px;"
        >
          <div>需整改问题</div>
          <el-button size="mini" style="color: blue;" @click="addTab"
            >添加</el-button
          >
        </div>
        <el-tabs
          v-if="complatedForm1.isIssues"
          v-model="editableTabsValue"
          type="card"
          :closable="complatedForm1.issuesItems.length > 1"
          style="margin: 10px 0 20px 50px;"
          @edit="handleTabsEdit"
        >
          <el-tab-pane
            v-for="item in complatedForm1.issuesItems"
            :key="item.name"
            :label="item.name"
            :name="item.name"
          >
            <div
              v-if="editableTabsValue === item.name"
              style="padding: 15px; border: 1px solid rgb(220, 223, 230);"
            >
              <div style="display: flex; margin-bottom: 20px;">
                <div style="width: 47%; margin-right: 15px;">
                  <div style="margin-bottom: 10px;">
                    <span style="color: red;">*</span>问题描述
                  </div>
                  <el-input
                    v-model="item.issuesRemark"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入"
                  />
                </div>
                <div style="width: 47%;">
                  <div style="margin-bottom: 10px;">
                    <span style="color: red;">*</span>整改要求
                  </div>
                  <el-input
                    v-model="item.rectificationRemark"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入"
                  />
                </div>
              </div>
              <div>
                <div style="margin-bottom: 10px;">
                  <span style="color: red;">*</span>照片
                </div>
                <el-upload
                  ref="upload"
                  class="new-task-upload1"
                  :auto-upload="false"
                  :http-request="noop"
                  :on-change="uploadFile2"
                  :show-file-list="false"
                  :multiple="true"
                  action="customize"
                >
                  <div class="file_upload">
                    <el-button
                      size="medium"
                      type
                      style="width: 200px; margin-right: 20px; color: blue;"
                      >上传附件</el-button
                    >
                    <div slot="tip" class="el-upload__tip">
                      支持jpg、png、jpeg
                    </div>
                  </div>
                </el-upload>
                <el-table :data="item.images" style="width: 100%;">
                  <el-table-column prop="name" label="文件名称" />
                  <el-table-column prop="size" label="文件大小">
                    <template slot-scope="{ row }">
                      <span>{{ `${row.size}${row.unit}` }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作">
                    <template slot-scope="{ row }">
                      <el-button type="text" @click="previewImg(row)"
                        >预览</el-button
                      >
                      <el-button type="text" @click="downloadImg(row)"
                        >下载</el-button
                      >
                      <el-button type="text" @click="deleteImg2(row)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <el-form-item
          v-if="complatedForm1.isIssues"
          label="立行整改"
          prop="isRectification"
          :rules="[{ required: true, message: '请选择', trigger: 'change' }]"
        >
          <el-radio-group v-model="complatedForm1.isRectification">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="complatedForm1.isIssues"
          label="问题类型"
          prop="issueType"
          :rules="[{ required: true, message: '请选择', trigger: 'change' }]"
        >
          <el-select
            v-model="complatedForm1.issueType"
            :popper-append-to-body="false"
            placeholder="请选择"
            ref="issuTypeselect"
          >
            <el-option
              v-for="item in issueTypeList"
              :key="item.id"
              :label="item.typeName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="complatedForm1.isIssues && complatedForm1.isRectification"
          label="整改结果"
          prop="rectificationRemark"
          :rules="[
            { required: true, message: '请输入整改结果', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="complatedForm1.rectificationRemark"
            type="textarea"
            :rows="3"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item
          v-if="complatedForm1.isIssues && complatedForm1.isRectification"
          label="现场整改情况："
          prop="rectificationImages"
        >
          <el-upload
            ref="upload"
            class="new-task-upload1"
            :auto-upload="false"
            :http-request="noop"
            :on-change="uploadFile3"
            :show-file-list="false"
            accept=".jpg,.png,.jpeg"
            :multiple="true"
            action="customize"
          >
            <div class="file_upload">
              <el-button
                size="medium"
                type
                style="width: 200px; margin-right: 20px; color: blue;"
                >上传附件</el-button
              >
              <div slot="tip" class="el-upload__tip">支持jpg、png、jpeg</div>
            </div>
          </el-upload>
          <el-table
            :data="complatedForm1.rectificationImages"
            style="width: 100%;"
          >
            <el-table-column prop="name" label="文件名称" />
            <el-table-column prop="size" label="文件大小">
              <template slot-scope="{ row }">
                <span>{{ `${row.size}${row.unit}` }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="{ row }">
                <el-button type="text" @click="previewImg(row)">预览</el-button>
                <el-button type="text" @click="downloadImg(row)"
                  >下载</el-button
                >
                <el-button type="text" @click="deleteImg3(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item
          v-if="complatedForm1.isIssues && !complatedForm1.isRectification"
          label="整改时限"
          prop="rectificationTimeLimit"
          :rules="[
            { required: true, message: '请选择整改时限', trigger: 'blur' }
          ]"
        >
          <el-date-picker
            v-model="complatedForm1.rectificationTimeLimit"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="complateDialogVisibled1 = false">取 消</el-button>
        <el-button type="primary" @click="finishTask1">确 定</el-button>
      </div>
    </el-dialog>
    <!--    点击附件图片弹框-->
    <el-dialog
      :visible.sync="imagePreviewVisible"
      top="10vh"
      :modal="true"
      width="600px"
      :modal-append-to-body="false"
    >
      <img
        :src="imagePreviewUrl"
        style="display: block; width: 500px; height: 500px; margin: 0 auto;"
        alt=""
      />
    </el-dialog>
    <!--    处理流程详情-->
    <el-dialog
      title="处理流程详情"
      :visible.sync="taskProcessDetailVisible"
      width="600px"
    >
      <div v-if="1">
        <el-row :gutter="20" type="flex">
          <el-col :span="5">
            <div class="infor-normal">指派人：</div>
          </el-col>
          <el-col :span="8">
            <a class="infor-normal">1231</a>
          </el-col>
        </el-row>
        <el-divider />
        <el-row
          :gutter="20"
          type="flex"
          align="middle"
          class="margin-bot-20"
          style="margin-bottom: 30px;"
        >
          <el-col :span="5">
            <div class="infor-normal">指派时间：</div>
          </el-col>
          <el-col :span="19">
            <a class="infor-normal"><span>21</span></a>
          </el-col>
        </el-row>
        <el-collapse v-model="activeName">
          <el-collapse-item name="1">
            <template slot="title">
              <div class="text-bold text-indent">事件内容</div>
            </template>
            <el-row :gutter="20" type="flex">
              <el-col :span="5">
                <div class="infor-normal">事件内容：</div>
              </el-col>
              <el-col :span="19">
                <a class="infor-normal">31</a>
                <div class="task-image">
                  <el-image
                    v-for="(pic, indexd) in picListChilds"
                    :key="indexd"
                    :src="pic"
                    fit="cover"
                    @click="openImg(indexd, 'picListChilds')"
                  />
                </div>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="2">
            <template slot="title">
              <div class="text-bold text-indent">事件附件</div>
            </template>
            <el-row :gutter="20" type="flex">
              <el-col :span="5">
                <div class="infor-normal">事件附件：</div>
              </el-col>
              <el-col :span="19">
                <div
                  v-if="fileListChilds.length > 0"
                  class="message-attachment"
                >
                  <div
                    v-for="(file, indexf) in fileListChilds"
                    :key="indexf"
                    class="attachment-content"
                  >
                    <img
                      v-if="file.type === 1"
                      src="@/assets/local_images/word.png"
                    />
                    <img
                      v-if="file.type === 2"
                      src="@/assets/local_images/excel.png"
                    />
                    <img
                      v-if="file.type === 3"
                      src="@/assets/local_images/pdf.png"
                    />
                    <img
                      v-if="file.type === 4"
                      src="@/assets/local_images/ppt.png"
                    />
                    <img
                      v-if="file.type === 5"
                      src="@/assets/local_images/zip.png"
                    />
                    <a
                      :href="file.url"
                      target="_blank"
                      :download="file.fileName"
                      >{{ file.fileName }}</a
                    >
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="3">
            <template slot="title">
              <div class="text-bold text-indent">子事件内容</div>
            </template>
            <el-row :gutter="20" type="flex">
              <el-col :span="5">
                <div class="infor-normal">子事件内容：</div>
              </el-col>
              <el-col :span="19">
                <a class="infor-normal">1</a>
                <div class="task-image">
                  <el-image
                    v-for="(pic, indexg) in picListChild"
                    :key="indexg"
                    :src="pic"
                    fit="cover"
                    @click="openImg(indexg, 'picListChild')"
                  />
                </div>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="4">
            <template slot="title">
              <div class="text-bold text-indent">子事件附件</div>
            </template>
            <el-row :gutter="20" type="flex">
              <el-col :span="5">
                <div class="infor-normal">子事件附件：</div>
              </el-col>
              <el-col :span="19">
                <div
                  v-if="fileListsChild.length > 0"
                  class="message-attachment"
                >
                  <div
                    v-for="(file, indexe) in fileListsChild"
                    :key="indexe"
                    class="attachment-content"
                  >
                    <img
                      v-if="file.type === 1"
                      src="@/assets/local_images/word.png"
                    />
                    <img
                      v-if="file.type === 2"
                      src="@/assets/local_images/excel.png"
                    />
                    <img
                      v-if="file.type === 3"
                      src="@/assets/local_images/pdf.png"
                    />
                    <img
                      v-if="file.type === 4"
                      src="@/assets/local_images/ppt.png"
                    />
                    <img
                      v-if="file.type === 5"
                      src="@/assets/local_images/zip.png"
                    />
                    <a
                      :href="file.url"
                      target="_blank"
                      :download="file.fileName"
                      >{{ file.fileName }}</a
                    >
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div v-else>没有数据</div>
      <span slot="footer" class="task-process-detail-style">
        <!-- <el-button @click="taskProcessDetailVisible = false">取 消</el-button> -->
        <el-button type="primary" @click="taskProcessDetailVisible = false"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
    <!--    执行者查看详情-->
    <el-dialog
      title="执行人详情"
      :visible.sync="flowChartDialogVisible"
      width="850px"
    >
      <flow-chart
        flow-chart-id="flowChart1"
        :flow-chart-data="flowChartData1"
        :flow-chart-width="800"
        :flow-chart-height="500"
      />
      <div slot="footer">
        <el-button @click="flowChartDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="flowChartDialogVisible = false"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="当前位置" :visible.sync="mapVisible" width="850px">
      <div id="map" ref="map" style="width: 800px; height: 500px;" />
    </el-dialog>
    <!-- 历史告警信息 -->
    <el-dialog
      title="历史告警信息"
      :visible.sync="historicalDataVisible"
      width="950px"
    >
      <el-table
        ref="alarmTable"
        :data="historicalList"
        :header-cell-style="{ background: '#F7FCFB' }"
        border
      >
        <el-table-column label="告警内容" align="center" prop="alarmContent" />
        <el-table-column
          v-if="
          eventDetail.stationTypeId === 17 ||
            eventDetail.stationTypeId === 16 ||
              (eventDetail.stationTypeId === 11 &&
                eventDetail.taskAlarmList &&
                eventDetail.taskAlarmList[0] &&
                eventDetail.taskAlarmList[0].type === 2)
          "
          label="告警图片"
          align="center"
          prop="alarmImage"
        >
          <template v-slot="{ row }">
            <div>
              <el-image
                v-for="(item, index) in row.alarmImage"
                :key="index"
                :src="item.url"
                style="width: 40px; height: 40px; margin-right: 10px; border-radius: 6px;"
                fit="cover"
                lazy
                @click="openImg(index, row.alarmImage)"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="告警项" align="center" prop="alarmItemName" />

        <el-table-column
          v-if="
            eventDetail.stationTypeId === 17 ||
              (eventDetail.stationTypeId === 11 &&
                eventDetail.taskAlarmList &&
                eventDetail.taskAlarmList[0] &&
                eventDetail.taskAlarmList[0].type === 2)
          "
          label="置信度"
          align="center"
          prop="confidence"
        />

        <el-table-column
          v-if="
            ![16, 17].includes(eventDetail.stationTypeId) &&
              !(
                eventDetail.stationTypeId === 11 &&
                eventDetail.taskAlarmList &&
                eventDetail.taskAlarmList[0] &&
                eventDetail.taskAlarmList[0].type === 2
              )
          "
          label="告警值"
          align="center"
          prop="alarmValue"
        >
          <template slot-scope="scope">
            {{ scope.row.alarmValue }}{{ scope.row.alarmItemUnit }}</template
          >
        </el-table-column>
        <el-table-column
          v-if="
            ![16, 17].includes(eventDetail.stationTypeId) &&
              !(
                eventDetail.stationTypeId === 11 &&
                eventDetail.taskAlarmList &&
                eventDetail.taskAlarmList[0] &&
                eventDetail.taskAlarmList[0].type === 2
              )
          "
          label="告警阈值"
          align="center"
          prop="alarmThreshold"
        >
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.alarmValueType === 0 ||
                  scope.row.alarmValueType === null
              "
            >
              {{ scope.row.alarmThreshold }}{{ scope.row.alarmItemUnit }}</span
            >
            <span v-if="scope.row.alarmValueType === 1">
              {{ scope.row.alarmMinValue
              }}{{ scope.row.alarmItemUnit }}&lt;,&lt;
              {{ scope.row.alarmMaxValue }}{{ scope.row.alarmItemUnit }}</span
            >
            <span v-if="scope.row.alarmValueType === 2"
              >&lt; {{ scope.row.alarmMinValue
              }}{{ scope.row.alarmItemUnit }},&gt; {{ scope.row.alarmMaxValue
              }}{{ scope.alarmItemUnit.unit }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="告警时间"
          width="250px"
          align="center"
          prop="alarmTime"
        >
          <template v-slot="{ row }">
            <div>
              {{
                row.alarmTime
                  ? eventDetail.stationTypeName === "环境噪声站点"
                    ? row.alarmTime.substring(0, 10)
                    : row.alarmTime.substring(0, 16)
                  : ""
              }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page.sync="pageNumber"
        layout="prev, pager, next, jumper"
        :total="total"
        style="margin-top: 15px;"
        @current-change="handleCurrentChange"
      />
    </el-dialog>
    <openImage
      v-if="resultImageState"
      :url-list="resultPicList"
      :url-index="resturlIndex"
      @closeImage="closeRestImage"
    />
    <openImage
      v-if="imageState"
      :url-list="urlImageList"
      :url-index="urlIndex"
      @closeImage="closeImage"
    />

    <!-- 生成任务调度弹窗 -->
    <enforceLawTask
      :newtask="newtask"
      :create-new="createNew"
      :event-detail="eventDetail"
      @success="handleSuccess"
    />
    <!-- 部门反馈 -->
    <el-dialog
      :title="'执行反馈'"
      :visible.sync="feedbackVisible"
      width="500px"
      class="feedback"
      :close-on-click-modal="false"
      @close="(feedbackVisible = false), (isReject = false), (nodeData = {})"
    >
      <div class="feedback-content">
        <div class="feedback-txt">
          <span>执行者： </span>
          <span>{{ nodeData.departmentName }} - {{ nodeData.userName }}</span>
        </div>
      </div>
      <div class="feedback-content">
        <div class="feedback-txt">
          <span>反馈时间： </span>
          <span>{{
            nodeData.completeTime ? nodeData.completeTime.substr(0, 16) : "--"
          }}</span>
        </div>
      </div>
      <div class="feedback-content">
        <div class="feedback-txt">
          <span>执行详情： </span>
          <span>{{ nodeData.completeRemark || "--" }}</span>
        </div>
      </div>
      <span slot="footer" class="feedback-style">
        <el-button
          v-if="btnList.includes(146)"
          class="search-button"
          @click="handleReject"
          >驳回</el-button
        >
        <el-button
          @click="
            (feedbackVisible = false), (isReject = false), (nodeData = {})
          "
          >关闭</el-button
        >
      </span>
    </el-dialog>
    <!-- 驳回节点弹框 -->
    <el-dialog
      v-loading="taskResultRejectLoading"
      element-loading-text="任务驳回提交中..."
      title="驳回任务"
      :visible.sync="taskResultRejectVisible"
      :close-on-click-modal="false"
      width="500px"
      @close="cancleRejectComplete('taskResultClose')"
    >
      <el-form
        ref="taskResultReject"
        :model="taskResultReject"
        :rules="taskResultRejectRules"
        label-width="100px"
      >
        <el-form-item label="驳回备注" prop="completeRemark">
          <el-row>
            <el-col :xs="24" :lg="22">
              <el-input
                v-model="taskResultReject.completeRemark"
                type="textarea"
                :rows="4"
              />
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <span slot="footer" class="task-result-style">
        <el-button @click="cancleRejectComplete('taskResultReject')"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="completeRejectTask('taskResultReject')"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!--    指派节点-->
    <el-dialog
      v-loading="taskAssignLoading"
      element-loading-text="任务指派提交中..."
      :close-on-click-modal="false"
      :visible.sync="dialogDrawer"
      :title="number === 1 ? '添加执行者' : '添加抄送者'"
      width="500px"
    >
      <el-form
        ref="addNode"
        :model="addNode"
        :rules="nodeRules"
        class="drawer-content"
        label-width="100px"
        label-position="right"
      >
        <el-form-item v-if="number === 1" label="执行者:" prop="obj">
          <el-cascader
            v-model="addNode.obj"
            collapse-tags
            :options="displayDepartmentList"
            :show-all-levels="true"
            :props="miltPerson"
            style="width: 100%;"
            placeholder="执行者"
          />
        </el-form-item>
        <el-form-item v-else label="抄送者:" prop="obj">
          <el-cascader
            v-model="addNode.obj"
            collapse-tags
            :options="displayDepartmentList"
            :show-all-levels="true"
            :props="miltPerson"
            style="width: 100%;"
            placeholder="抄送者"
          />
        </el-form-item>
        <el-form-item
          v-if="number === 1"
          label="任务描述:"
          prop="taskNodeContent"
        >
          <el-input
            v-model="addNode.taskNodeContent"
            size="medium"
            style="width: 100%;"
            type="textarea"
            rows="5"
            maxlength="500"
          />
        </el-form-item>
        <el-form-item v-if="number === 1" label="附件:">
          <uploadImage
            v-if="dialogDrawer"
            style="width: 100%;"
            @getBase64ImgList="getAssignedBase64ImgList"
          />
        </el-form-item>
        <div class="footer">
          <el-button class="search-button" @click="handleaddNode"
            >确定</el-button
          >
          <el-button style="margin-left: 50px;" @click="handleClose"
            >关闭</el-button
          >
        </div>
      </el-form>
    </el-dialog>
    <!-- 关闭任务弹框 -->
    <el-dialog
      v-loading="taskResultCloseLoading"
      :element-loading-text="
        isNodeClosed ? '任务退单提交中...' : '任务关闭提交中...'
      "
      :title="isNodeClosed ? '任务退单' : '任务关闭'"
      :visible.sync="taskResultCloseVisible"
      :close-on-click-modal="false"
      width="500px"
      @close="cancleCloseComplete('taskResultClose')"
    >
      <!-- :rules="taskResultCloseRules" -->
      <el-form
        ref="taskResultClose"
        :model="taskResultClose"
        label-width="100px"
      >
        <el-form-item
          :label="isNodeClosed ? '退单备注' : '关闭备注'"
          prop="completeRemark"
          :rules="[
            {
              required: true,
              message: isNodeClosed
                ? '请填写任务退单备注'
                : '请填写任务关闭备注',
              trigger: 'blur'
            },
            {
              min: 3,
              max: 256,
              message: '长度在 3 到 256 个字符',
              trigger: 'blur'
            }
          ]"
        >
          <el-row>
            <el-col :xs="24" :lg="22">
              <el-input
                v-model="taskResultClose.completeRemark"
                type="textarea"
                maxlength="255"
                show-word-limit
                :rows="4"
              />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item v-if="isNodeClosed" label="附件:">
          <uploadImage
            v-if="taskResultCloseVisible"
            style="width: 100%;"
            @getBase64ImgList="getCloseBase64ImgList"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="task-result-style">
        <el-button @click="cancleCloseComplete('taskResultClose')"
          >取 消</el-button
        >
        <el-button type="primary" @click="completeCloseTask('taskResultClose')"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <el-dialog
      v-if="dialogVisible"
      title="详情"
      :visible.sync="dialogVisible"
      width="620px"
      :close-on-click-modal="false"
      @close="dialogVisible = false"
    >
      <section
        style="display: flex; flex-direction: column; gap: 18px; font-size: 14px;"
      >
        <div>
          <span style="color: #999999;">告警时间：</span>
          <span>{{ formData.alarmTime }}</span>
        </div>

        <div>
          <span style="color: #999999;">告警类型：</span>
          <span style="color: #f77222;">{{ formData.alarmItemName }}</span>
        </div>

        <div style="height: 1px; background: #e6e6e6;"></div>
      </section>
      <footer style="margin-top: 27px;">
        <header style="display: flex; gap: 7px;">
          <div
            style="width: 3px; height: 16px; background: #3bb66f; border-radius: 2px;"
          ></div>
          <span>告警内容及片段</span>
        </header>
      </footer>
      <div style="margin-top: 24px; margin-bottom: 13px;">
        {{ formData.alarmContent }}
      </div>
      <div
        v-if="
          formData.stationTypeId === 17 ||
            (formData.stationTypeId === 11 && formData.type === 2)
        "
        style="margin-bottom: 10px;"
      >
        <span>置信度：</span>
        <span>{{ formData.confidence }}</span>
      </div>
      <div v-if="formData.alarmImage" style="display: flex;">
        <el-image
          v-for="(item, index) in formData.alarmImage"
          :key="index"
          :src="item.url"
          style="width: 180px; height: 120px; margin-right: 10px; border-radius: 6px;"
          fit="cover"
          lazy
          @click="openImg(index, formData.alarmImage)"
        />
      </div>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 监控弹窗 -->
    <el-dialog
      :visible.sync="monitorDialogVisible"
      title="监控播放"
      destroy-on-close
      width="fit-content"
      top="8vh"
    >
      <div style="margin-bottom: 20px;">
        <el-select
          v-model="selectedMonitor"
          placeholder="选择监控"
          style="width: 300px;"
          @change="handleMonitorChange"
        >
          <el-option
            v-for="item in monitorList"
            :key="item.monitorId"
            :value="item.monitorId"
            :label="item.monitorName"
          />
        </el-select>
      </div>
      <div v-loading="monitorLoading" style="width: 50vw; height: 60vh;">
        <LivePlayer
          v-if="videoUrl && monitorDialogVisible"
          :vidoe-url="videoUrl"
          style="width: 100%; height: 100%;"
        />
        <div
          v-else
          style="display: flex; align-items: center; justify-content: center; width: 100%; height: 100%;"
        >
          <span v-if="!monitorLoading">请选择监控</span>
        </div>
      </div>
    </el-dialog>
  </section>
</template>
<script>
/* eslint-disable */
import { formatDate } from "@/utils/date";
import { download } from "@/utils/downloadFile";
import BMap from "BMap";
import { getLawEventDetail, getHistoricalData, taskPush } from "@/api/jointEnforcement";
import { mapGetters } from "vuex";
import { getAlarmDetail, getMonitorList } from "@/api/alarmManagement";
import { upload } from "@/api/enforceLawDetails";
import openImage from "@/views/emergencyManagement/modules/openImage";

import {
  completeNodeTask,
  completeTask,
  rejectNodeTask,
  getTopTaskExecutorList,
  assignNodeTask,
  closeNodeTask,
  closeTask,
  getIssueTypeList
} from "@/api/taskManagement";
import flowChart from "./index";
import enforceLawTask from "./enforceLawTask";
import uploadImage from "@/components/uploadImage/index.vue";
import ComImage from "@/components/image/src/main.vue";
import LivePlayer from "@/views/camera/livePlayer.vue";
import { getWaterLive } from '@/api/equipManagement'

const FILE_TYPE_DIC = {
  png: 0,
  jpg: 0,
  jpeg: 0,
  bmp: 0,
  gif: 0,
  docx: 1,
  doc: 1,
  xlsx: 2,
  xls: 2,
  pdf: 3,
  pptx: 4,
  ppt: 4,
  rar: 5,
  zip: 5
};
const IMG_TYPE_DIC = {
  png: 0,
  jpg: 0,
  jpeg: 0
};
export default {
  name: "EnforceLawDetails",
  components: {
    flowChart,
    enforceLawTask,
    openImage,
    uploadImage,
    ComImage,
    LivePlayer
  },
  props: {
    id: {
      type: Number,
      default: undefined
    },
    taskId: {
      type: String,
      default: null
    },
    imgs: {
      type: String,
      default: null
    },
    page: {
      type: Number,
      default: 1
    },
    lawQuery: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const objValidator = function(rule, value, callback) {
      if (JSON.stringify(value) === "{}") {
        callback(new Error("请选择执行者"));
      }
      callback();
    };
    return {
      formData: null,
      dialogVisible: false,
      editableTabsValue: "问题1",
      tabIndex: 1,
      taskAnnexList: [],
      stationType: {
        1: "微控站点",
        2: "区控站点",
        3: "市控站点",
        4: "国控站点"
      },
      feedbackVisible: false,
      nodeData: {},
      taskResultRejectLoading: false,
      taskResultRejectVisible: false,
      taskResultReject: {
        completeRemark: undefined
      },
      record: 0,
      taskResultRejectRules: {
        completeRemark: [
          { required: true, message: "请填写任务驳回备注", trigger: "blur" },
          {
            min: 3,
            max: 256,
            message: "长度在 3 到 256 个字符",
            trigger: "blur"
          }
        ]
      },
      isReject: false,
      pageNum: 1,
      pageSize: 10,
      createNew: false,
      lawListData: [],
      finishTaskLoading: false,
      alarmTypes: [
        {
          value: 2,
          label: "空气站点"
        },
        {
          value: 1,
          label: "河流水质站点"
        },
        // {
        //   value: 3,
        //   label: '重点污染源'
        // },
        // {
        //   value: 4,
        //   label: '巡岗上报'
        // },
        {
          value: 5,
          label: "在建工地站点"
        },
        {
          value: 6,
          label: "印刷工厂站点"
        },
        {
          value: 7,
          label: "车辆汽修站点"
        },
        {
          value: 8,
          label: "餐饮站点"
        },
        {
          value: 9,
          label: "加油站点"
        },
        {
          value: 10,
          label: "政企站点"
        }
      ],
      newtask: {},
      isNodeCompleted: false,
      activeLeftNames: ["1", "2", "3", "4", "5", "6", "7", "8"],
      activeRightNames: ["1", "2", "3"],
      activeName: ["1", "2", "3"],
      complateDialogVisibled: false,
      complateDialogVisibled1: false,
      currNodeData: {},
      eventDetail: {},
      complatedForm: {
        content: undefined,
        fileList: []
      },
      complatedForm1: {
        issueType: "",
        completeRemark: "",
        taskAnnexList: [],
        isIssues: true,
        issuesItems: [
          {
            name: "问题1",
            issuesRemark: "",
            rectificationRemark: "",
            images: []
          }
        ],
        isRectification: true,
        rectificationRemark: "",
        rectificationImages: [],
        rectificationTimeLimit: ""
      },
      show: true,
      searchListData: [],
      activeName2: "first",
      read: [],
      executiveFeedbackList: [],
      unread: [],
      fileList: [],
      imagePreviewVisible: false,
      flowChartDialogVisible: false,
      historicalList: [],
      total: 0,
      pageNumber: 1,
      historicalDataVisible: false,
      picListChilds: [],
      picListChild: [],
      picList: undefined,
      fileListChilds: [],
      fileListsChild: [],
      imagePreviewUrl: "",
      flowChartData: {
        linkDataArray: [],
        nodeDataArray: []
      },
      mapVisible: false,
      currShow: false,
      flowChartData1: {
        class: "go.TreeModel",
        nodeDataArray: []
      },
      taskProcessDetailVisible: false,
      fileLists: [],
      taskFinishedDetailList: [],
      resultImageState: false,
      resultPicList: [],
      resturlIndex: 0,
      taskDetail: {},
      taskDetailLoading: false,
      icon5: require("@/assets/images/<EMAIL>"),
      icon6: require("@/assets/images/<EMAIL>"),
      icon0: require("@/assets/images/toux.png"),
      icon1: require("@/assets/images/tou1.png"),
      icon2: require("@/assets/images/toux_2.png"),
      icon3: require("@/assets/images/tou3.png"),
      dialogDrawer: false,
      number: 1,
      addNode: {
        taskNodeContent: undefined,
        obj: {}
      },
      nodeRules: {
        obj: [{ validator: objValidator, required: true, trigger: "change" }]
      },
      miltPerson: {
        emitPath: false,
        multiple: false,
        expandTrigger: "hover",
        value: "needsData",
        children: "departmentAccountList",
        label: "departmentName",
        accountId: "",
        disabled: "disabled",
        depId: ""
      },
      isAdd: false,
      taskAssignLoading: false,
      displayDepartmentList: [],
      withStreet: 1,
      taskResultCloseLoading: false,
      taskResultCloseVisible: false,
      taskResultClose: {
        completeRemark: undefined
      },
      taskResultCloseRules: {
        completeRemark: [
          { required: true, message: "请填写任务退单备注", trigger: "blur" },
          {
            min: 3,
            max: 256,
            message: "长度在 3 到 256 个字符",
            trigger: "blur"
          }
        ]
      },
      isNodeClosed: false,

      issueTypeList: [],
      imageState: false,
      urlImageList: [],
      urlIndex: 0,
      // 对应的监控路由name
      monitorRouteMap: {
        16: "highCamera",
        17: "panoramicMonitor"
      },

      // 监控弹窗相关
      monitorDialogVisible: false,
      monitorList: [],
      selectedMonitor: null,
      videoUrl: null,
      monitorLoading: false
    };
  },
  computed: {
    ...mapGetters([
      "userId",
      "isAllJoinTaskOperate",
      "departmentId",
      "departmentName",
      "userId",
      "departmentType",
      "btnList"
    ])
  },
  mounted() {
    this.getIssueTypeList();
    this.getFirst();
    this.getHistoricalData(this.taskId);
    this.getPersonList();
  },
  methods: {
    handelScroll() {
      this.$refs.issuTypeselect.blur();
    },
    async getIssueTypeList() {
      const res = await getIssueTypeList();
      this.issueTypeList = res.data.data;
    },
    // 预览pdf
    previewPDF(row) {
      window.open(row.pdfUrl);
    },
    // 下载pdf
    downloadPDF(row) {
      download(row.pdfUrl, `${row.name}问题清单`);
    },
    uploadFile1(file) {
      const fileType = file.name.split(".").pop();
      if (IMG_TYPE_DIC[fileType] !== 0) {
        this.$message.error("文件格式不正确");
        return;
      }
      const formData = new FormData();
      formData.append("file", file.raw);
      upload(formData).then(res => {
        this.$set(res.data.data, "annexUrl", res.data.data.url);
        this.$set(res.data.data, "format", res.data.data.type);
        this.complatedForm1.taskAnnexList.push(res.data.data);
      });
    },
    uploadFile2(file) {
      const fileType = file.name.split(".").pop();
      if (IMG_TYPE_DIC[fileType] !== 0) {
        this.$message.error("文件格式不正确");
        return;
      }
      const formData = new FormData();
      formData.append("file", file.raw);
      upload(formData).then(res => {
        const obj = this.complatedForm1.issuesItems.find(
          item => item.name === this.editableTabsValue
        );
        if (obj.images.length === 4) {
          this.$message.warning("最多上传4张照片");
          return;
        }
        obj.images.push(res.data.data);
      });
    },
    uploadFile3(file) {
      const fileType = file.name.split(".").pop();
      if (IMG_TYPE_DIC[fileType] !== 0) {
        this.$message.error("文件格式不正确");
        return;
      }
      if (this.complatedForm1.rectificationImages.length === 4) {
        this.$message.warning("最多上传4张照片");
        return;
      }
      const formData = new FormData();
      formData.append("file", file.raw);
      upload(formData).then(res => {
        this.complatedForm1.rectificationImages.push(res.data.data);
      });
    },
    // 预览
    previewImg(row) {
      this.imagePreviewUrl = row.url;
      this.imagePreviewVisible = true;
    },
    // 下载
    downloadImg(row) {
      download(row.url, row.name);
    },
    // 删除
    deleteImg1(row) {
      this.complatedForm1.taskAnnexList.forEach((item, index) => {
        if (row.url === item.url) {
          this.complatedForm1.taskAnnexList.splice(index, 1);
        }
      });
    },
    deleteImg2(row) {
      const obj = this.complatedForm1.issuesItems.find(
        item => item.name === this.editableTabsValue
      );
      obj.images.forEach((item, index) => {
        if (row.url === item.url) {
          obj.images.splice(index, 1);
        }
      });
    },
    deleteImg3(row) {
      this.complatedForm1.rectificationImages.forEach((item, index) => {
        if (row.url === item.url) {
          this.complatedForm1.rectificationImages.splice(index, 1);
        }
      });
    },
    // 关闭完成
    handleCloseComplate() {
      this.tabIndex = 1;
      this.complatedForm1 = {
        completeRemark: "",
        taskAnnexList: [],
        isIssues: true,
        issuesItems: [
          {
            name: "问题1",
            issuesRemark: "",
            rectificationRemark: "",
            images: []
          }
        ],
        isRectification: true,
        rectificationRemark: "",
        rectificationImages: [],
        rectificationTimeLimit: ""
      };
    },
    addTab() {
      this.tabIndex++;
      this.editableTabsValue = `问题${this.tabIndex}`;
      this.complatedForm1.issuesItems.push({
        name: this.editableTabsValue,
        issuesRemark: "",
        rectificationRemark: "",
        images: []
      });
    },
    // tabs编辑
    handleTabsEdit(targetName, action) {
      if (action === "remove") {
        let tabs = this.complatedForm1.issuesItems;
        let activeName = this.editableTabsValue;
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if (tab.name === targetName) {
              let nextTab = tabs[index + 1] || tabs[index - 1];
              if (nextTab) {
                this.editableTabsValue = nextTab.name;
              }
            }
          });
        }

        this.editableTabsValue = activeName;
        this.complatedForm1.issuesItems = tabs.filter(
          tab => tab.name !== targetName
        );
      }
    },
    Refresh() {
      this.getFirst();
    },
    handleCurrentChange(page) {
      this.pageNumber = page;
      this.getHistoricalData(this.taskId);
    },
    getFirst() {
      if (this.id) {
        this.getTaskDetail();
      } else if (this.taskId) {
        this.getLawEventDetail();
      }
    },
    // 获取巡岗跳转的详情
    getTaskDetail() {
      getLawEventDetail(this.taskId, this.userId).then(res => {
        if (res.data.data) {
          this.eventDetail = res.data.data;
          this.taskDetail = this.eventDetail;
          this.searchListData = this.eventDetail.taskAlarmList;
          this.flowChartData = {
            nodeDataArray: this.eventDetail.taskNodeList.map(item => {
              item.key = item.taskNodeId;
              if (Number(item.completeStatus) === 0) {
                item.avatar = this.icon0;
              } else if (Number(item.completeStatus) === 1) {
                item.avatar = this.icon1;
              } else {
                item.avatar = this.icon3;
              }
              if (item.permission === 1) {
                item.avatar = this.icon2;
              }
              if (item.taskNodeType === 1) {
                item.avatar = this.icon5;
              }
              if (item.taskNodeType === 0) {
                item.avatar = this.icon6;
              }
              delete item.taskNodeId;
              return item;
            }),
            linkDataArray: this.eventDetail.taskNodeLinkList
          };
          this.executiveFeedbackList = this.eventDetail.executiveFeedbackList.map(
            item => {
              item.accountName = `${
                item.departmentName !== null ? item.departmentName : ""
              }-${item.userName !== null ? item.userName : ""}${
                item.departmentName === null && item.userName === null
                  ? ""
                  : ":"
              }`;
              item.taskAnnexList.sort((a, b) => {
                if (!["png", "jpg", "jpeg", "gif", "bmp"].indexOf(a.format)) {
                  return -1;
                } else {
                  return 1;
                }
              });
              return item;
            }
          );
          this.read = this.eventDetail.read;
          this.unread = this.eventDetail.unread;
        }
        this.picList =
          this.eventDetail.taskAnnexList === null
            ? []
            : this.eventDetail.taskAnnexList;
        // this.picList = this.imgs ? this.imgs.split(',').map(v => {
        //   return { annexUrl: v }
        // }) : []
      });
    },
    parseTime(time) {
      return formatDate(new Date(time), "yyyy年MM月");
    },
    getLawEventDetail() {
      this.taskDetailLoading = true;
      getLawEventDetail(this.taskId, this.userId)
        .then(res => {
          if (res.data.data) {
            this.eventDetail = res.data.data;
            this.taskDetail = this.eventDetail;
            this.searchListData = this.eventDetail.taskAlarmList;
            this.flowChartData = {
              nodeDataArray: this.eventDetail.taskNodeList.map(item => {
                item.key = item.taskNodeId;
                // item.avatar = Number(item.completeStatus) === 0
                // ? this.icon0 : Number(item.completeStatus) === 1
                // ? this.icon1 : this.icon3
                if (Number(item.completeStatus) === 0) {
                  item.avatar = this.icon0;
                } else if (Number(item.completeStatus) === 1) {
                  item.avatar = this.icon1;
                } else {
                  item.avatar = this.icon3;
                }
                if (item.permission === 1) {
                  item.avatar = this.icon2;
                }
                if (item.taskNodeType === 1) {
                  item.avatar = this.icon5;
                }
                if (item.taskNodeType === 0) {
                  item.avatar = this.icon6;
                }
                delete item.taskNodeId;
                return item;
              }),
              linkDataArray: this.eventDetail.taskNodeLinkList
            };
            this.executiveFeedbackList = this.eventDetail.executiveFeedbackList.map(
              item => {
                item.accountName = `${
                  item.departmentName !== null ? item.departmentName : ""
                }${
                  item.departmentName === null && item.userName === null
                    ? ""
                    : "-"
                }${
                  item.departmentName === null && item.userName === null
                    ? ""
                    : "-"
                }${item.userName !== null ? item.userName : ""}${
                  item.departmentName === null && item.userName === null
                    ? ""
                    : ":"
                }`;
                item.taskAnnexList.sort((a, b) => {
                  if (!["png", "jpg", "jpeg", "gif", "bmp"].indexOf(a.format)) {
                    return -1;
                  } else {
                    return 1;
                  }
                });
                return item;
              }
            );
            this.read = this.eventDetail.read;
            this.unread = this.eventDetail.unread;
            this.picList =
              this.eventDetail.taskAnnexList === null
                ? []
                : this.eventDetail.taskAnnexList;
            // // this.handleCheck()
            // const piclist = this.eventDetail.taskLogList.taskAnnexList
            //  ? this.eventDetail.taskLogList.taskAnnexList.
            //  split(',').filter((item) => FILE_TYPE_DIC[item.split('.').pop()] === 0) : []
            // if (this.eventDetail.taskLogList.taskAnnexList) {
            // const piclist = this.eventDetail.taskLogList
            // .taskAnnexList.split(',')
            // .filter((item) => FILE_TYPE_DIC[item.split('.').pop()] === 0)
            // } else {
            //   const piclist = []
            // }
            // const fileList = this.eventDetail.completeUrl
            //  ? this.eventDetail.completeUrl.split(',')
            //  .filter(item => FILE_TYPE_DIC[item.split('.').pop()] !== 0).map(item => {
            //   return {
            //     fileName: item.split('/').pop(),
            //     type: FILE_TYPE_DIC[item.split('.').pop()],
            //     url: item
            //   }
            // }) : []
            // const taskFinishedDetailList = {
            //   avatarUrl: this.eventDetail.avatarUrl,
            //   piclist: piclist,
            //   fileList: fileList,
            //   accountName: this.eventDetail.accountName,
            //   completeTime: this.eventDetail.completeTime,
            //   description: this.eventDetail.description
            // }
            // this.taskFinishedDetailList = [taskFinishedDetailList]
            // this.fileLists = this.eventDetail.annexUrl === null
            //  ? [] : this.eventDetail.annexUrl.split(',').filter
            //  ((item) => FILE_TYPE_DIC[item.split('.').pop()] !== 0).map((item) => {
            //   return {
            //     fileName: item.split('/').pop(),
            //     type: FILE_TYPE_DIC[item.split('.').pop()],
            //     url: item
            //   }
            // })
          }
        })
        .finally(() => {
          this.taskDetailLoading = false;
          // this.eventDetail.myTaskNodeList = [
          //   {completeStatus: 0,
          //     departmentName: '123',
          //     userName: '321',
          //     taskNodeContent: '456',
          //     taskNodeId: 123,
          //     eventTypeId: 0
          //   }
          // ]
          // console.log(this.eventDetail, 'this.eventDetail')
        });
    },
    handleCheck() {
      getAlarmDetail({ enforcementTaskId: this.eventDetail.id }).then(res => {
        const { data } = res.data;
        if (data && data.length) {
          this.searchListData = data;
        } else {
          this.searchListData = [];
        }
      });
    },
    handleCreateTask(row) {
      if (this.picList !== undefined) {
        this.$router.push({
          name: "myTask",
          query: {
            title: this.$route.query.title,
            content: row.content,
            eventId: row.taskId,
            annexUrl: this.eventDetail.annexUrl
              ? this.eventDetail.annexUrl
              : "",
            show: true
          }
        });
      } else {
        this.$router.push({
          name: "myTask",
          query: {
            title: row.title,
            content: row.content,
            eventId: row.taskId,
            show: true
          }
        });
      }
      // const pollutantType
    },
    connectPlan(row) {
      if (this.picList !== undefined) {
        this.newtask = row;
        this.createNew = true;
        //   this.$router.push({
        //   name: 'myTask',
        //   query: {
        //     title: this.$route.query.title,
        //     content: row.content,
        //     eventId: row.taskId,
        //     parentId: this.$route.query.taskId,
        //     item: 'false',
        //     show: true,
        //     isPlan: true,
        //     annexUrl: this.eventDetail.annexUrl ? this.eventDetail.annexUrl : '',
        //     eventTypeId: row.eventTypeId,
        //     stationId: row.stationId
        //   }
        // })
      } else {
        this.newtask = row;
        this.createNew = true;
        //   this.$router.push({
        //   name: 'myTask',
        //   query: {
        //     title: row.title,
        //     content: row.content,
        //     eventId: row.taskId,
        //     parentId: row.taskId,
        //     item: 'false',
        //     show: true,
        //     isPlan: true,
        //     eventTypeId: row.eventTypeId,
        //     stationId: row.stationId
        //   }
        // })
      }
    },
    connectTask(row) {
      this.$router.push({
        name: "myTask",
        query: {
          taskId: row.eventId ? row.eventId : this.$route.query.taskId,
          parentId: this.$route.query.taskId
        }
      });
    },
    handleComplated(args) {
      this.currNodeData = args;
      this.isNodeCompleted = args.parentId;
      if (this.eventDetail.eventTypeId === 0) {
        this.complateDialogVisibled1 = true;
      } else {
        this.complateDialogVisibled = true;
      }
    },
    handleDetail(args) {
      const { data } = args;
      this.nodeData = data;
      if (
        (!data.parentId &&
          (Number(data.completeStatus) === 1 ||
            Number(data.completeStatus) === 2)) ||
        (Number(data.permission) === 2 &&
          (Number(data.completeStatus) === 1 ||
            Number(data.completeStatus) === 2))
      ) {
        this.feedbackVisible = true;
        this.isTaskClose = Number(data.completeStatus) === 2;
      }
      if (Number(data.completeStatus) === 1 && this.isAllJoinTaskOperate) {
        this.feedbackVisible = true;
        this.isReject = Boolean(this.isAllJoinTaskOperate);
      } else if (
        Number(data.permission) === 2 &&
        Number(data.completeStatus) === 1
      ) {
        const currLinkObj = this.flowChartData.linkDataArray.filter(
          item => item.to === data.key
        )[0];
        const currNodeObj = this.flowChartData.nodeDataArray.filter(
          item => item.key === currLinkObj.from
        )[0];
        this.isReject = currNodeObj.userId === this.userId;
      }
    },
    handleReject() {
      this.taskResultRejectVisible = true;
    },
    // 驳回任务
    completeRejectTask(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$confirm("此操作将驳回任务, 是否驳回?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            customClass: "customConfirm",
            type: "warning",
            center: true
          })
            .then(() => {
              this.taskResultRejectLoading = true;
              const newTime = new Date();
              if (newTime.getTime() - this.record > 400) {
                rejectNodeTask(
                  this.nodeData.key,
                  this.userId,
                  this.taskResultReject.completeRemark
                )
                  .then(res => {
                    this.$message({
                      type: "success",
                      message: "驳回任务成功"
                    });
                    this.taskResultRejectVisible = false;
                    this.taskResultRejectLoading = false;
                    this.feedbackVisible = false;
                    // this.fetchtaskDetail()
                    this.getFirst();
                    // this.returnToTable()
                  })
                  .catch(err => {
                    throw new Error(err);
                  })
                  .finally(() => {
                    // this.taskResultCloseVisible = false
                    this.taskResultRejectLoading = false;
                  });
              }
              this.record = new Date().getTime();
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消驳回"
              });
            });
        } else {
          return false;
        }
      });
    },
    cancleRejectComplete() {
      this.$refs.taskResultReject.resetFields();
      this.taskResultRejectVisible = false;
    },
    opencompleted(item) {
      this.$refs.flowChart.tmDisplay = false;
      this.currNodeData = item;
      this.currNodeData.key = item.taskNodeId;
      this.isNodeCompleted = true;
      if (this.eventDetail.eventTypeId === 0) {
        this.complateDialogVisibled1 = true;
      } else {
        this.complateDialogVisibled = true;
      }
    },
    returnBack() {
      this.$emit("toPage", this.page);
      this.$emit("returnToTable");
      this.$emit("search", this.lawQuery);
    },
    handleComplted() {
      this.$confirm(
        "此操作将完成任务，未完成的人员将自动退单，是否完成任务？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          customClass: "customConfirm",
          type: "warning",
          center: true
        }
      )
        .then(res => {
          this.complateDialogVisibled = true;
          this.isNodeCompleted = false;
          // completeTask(this.taskId, this.userId).then(res => {
          //   this.$message.success('完成任务成功')
          //   this.getFirst()
          // })
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消完成任务"
          });
        });
    },
    uploadFile(file, fileList) {
      // 利用fileReader对象获取file
      const that = this;
      this.complatedForm.fileList = [];
      fileList.forEach((item, index) => {
        const rawFile = item.raw;
        const filename = rawFile.name;
        const filetype = rawFile.name.split(".").pop();
        if (FILE_TYPE_DIC[filetype] !== 0) {
          this.$message.error("只能上传image文件");
          fileList.splice(index, 1);
          return;
        }
        const reader = new FileReader();
        reader.readAsDataURL(rawFile);
        reader.onload = function(e) {
          // 读取到的图片base64 数据编码 将此编码字符串传给后台即可
          that.complatedForm.fileList.push({
            data: e.target.result.split(",")[1], // .split(',')[1]
            name: filename,
            format: filetype
          });
        };
      });
    },
    handleSuccess() {
      this.createNew = false;
      this.newtask = {};
      this.getFirst();
      this.getHistoricalData(this.taskId);
    },
    handleFilePreview(file) {
      const rawFile = file.raw;
      const filetype = rawFile.name.split(".").pop();
      if (FILE_TYPE_DIC[filetype] !== 0) return;
      const reader = new FileReader();
      const that = this;
      reader.readAsDataURL(rawFile);
      reader.onload = function(e) {
        that.imagePreviewUrl = e.target.result;
      };
      this.imagePreviewVisible = true;
    },
    fileBeforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleFileRemove(file, fileList) {
      this.uploadFile(file, fileList);
    },
    noop() {
      // el-update http-request 不处理 noop 方法
    },
    map() {
      const map = new BMap.Map("map", { enableMapClick: false });
      map.centerAndZoom(
        new BMap.Point(
          Number(this.searchListData[0].stationLng),
          Number(this.searchListData[0].stationLat)
        ),
        14
      ); // 初始化地图,设置城市和地图级别。
      map.enableScrollWheelZoom();
      map.setMapStyleV2({
        styleId: "62515f0fc623b51ecba40a7c72d766c2"
      });
      const marker = new BMap.Marker(
        new BMap.Point(
          Number(this.searchListData[0].stationLng),
          Number(this.searchListData[0].stationLat)
        )
      );
      map.addOverlay(marker);

      // 处理百度地图未放在标准文档流中，会出现放大或缩小后,中心点偏移(中心点不是在放大前的点)
      // 临时存储地图中心点经度和纬度
      let center_lng = 0;
      let center_lat = 0;
      // 监听地图缩放开始事件 lng表示经度，lat表示纬度
      map.addEventListener("zoomstart", function(e) {
        center_lng = map.getCenter().lng;
        center_lat = map.getCenter().lat;
      });
      // 监听地图缩放结束事件 lng表示经度，lat表示纬度
      map.addEventListener("zoomend", function(e) {
        map.centerAndZoom(
          new BMap.Point(center_lng, center_lat),
          map.getZoom()
        );
      });
    },
    // 查看地址
    maps() {
      const map = new BMap.Map("map", { enableMapClick: false });
      map.centerAndZoom(
        new BMap.Point(
          Number(this.eventDetail.lng),
          Number(this.eventDetail.lat)
        ),
        14
      ); // 初始化地图,设置城市和地图级别。
      map.enableScrollWheelZoom();
      map.setMapStyleV2({
        styleId: "62515f0fc623b51ecba40a7c72d766c2"
      });
      const marker = new BMap.Marker(
        new BMap.Point(
          Number(this.eventDetail.lng),
          Number(this.eventDetail.lat)
        )
      );
      map.addOverlay(marker);

      // 处理百度地图未放在标准文档流中，会出现放大或缩小后,中心点偏移(中心点不是在放大前的点)
      // 临时存储地图中心点经度和纬度
      let center_lng = 0;
      let center_lat = 0;
      // 监听地图缩放开始事件 lng表示经度，lat表示纬度
      map.addEventListener("zoomstart", function(e) {
        center_lng = map.getCenter().lng;
        center_lat = map.getCenter().lat;
      });
      // 监听地图缩放结束事件 lng表示经度，lat表示纬度
      map.addEventListener("zoomend", function(e) {
        map.centerAndZoom(
          new BMap.Point(center_lng, center_lat),
          map.getZoom()
        );
      });
    },
    handleClickMap() {
      this.mapVisible = true;
      this.$nextTick(() => {
        this.map();
      });
    },
    // 历史数据
    handleHistorical() {
      this.$refs.flowChart.tmDisplay = false;
      this.historicalDataVisible = true;
    },
    handleHistory(task) {
      const stationTypeList = {
        1: "waterSite",
        2: "airSite",
        3: "noiseMonitoring",
        5: "constructionPlace",
        6: "print",
        7: "garage",
        8: "restaurant",
        9: "gas",
        10: "companyMonitoring",
        11: "historyMonitor",
        12: "electricityMonitoring",
        13: "solidWasteMonitoring"
      };
      this.$refs.flowChart.tmDisplay = false;
      this.$router.push({
        name: stationTypeList[task.stationTypeId],
        query: {
          taskId: task.taskId,
          id: task.stationId
        }
      });
    },
    gethistoricalList() {
      this.taskDetailLoading = true;
      this.getHistoricalData();
    },
    getHistoricalData(data) {
      getHistoricalData(this.pageNumber, this.pageSize, data)
        .then(res => {
          const datas = res.data.data;
          if (datas) {
            this.historicalList = datas.records;
            this.total = datas.total;
          } else {
            this.historicalList = [];
            this.total = 0;
          }
        })
        .finally(() => {
          this.taskDetailLoading = false;
        });
    },
    // 查看监控
    checkMonitor() {
      this.monitorDialogVisible = true;
      this.getMonitorList();
    },
    // 获取监控列表
    getMonitorList() {
      getMonitorList(this.taskId).then(res => {
        this.monitorList = res.data.data || [];
        if (this.monitorList.length > 0) {
          this.selectedMonitor = this.monitorList[0].monitorId;
          this.playMonitor();
        }
      });
    },
    // 监控下拉选择变化
    handleMonitorChange() {
      this.playMonitor();
    },
    // 播放监控
    playMonitor() {
      this.videoUrl = null;
      this.monitorLoading = true;
      getWaterLive(this.selectedMonitor)
        .then(res => {
          this.videoUrl = res.data.data.flvHttps;
        })
        .finally(() => {
          this.monitorLoading = false;
        });
    },
    handleClickMaps() {
      this.mapVisible = true;
      this.$nextTick(() => {
        this.maps();
      });
    },
    flowChartNodeClick() {
      this.taskProcessDetailVisible = true;
    },
    flowChartTreeCollapsed({ node }) {
      this.taskProcessDetailVisible = false;
    },
    flowChartTreeExpanded({ node }) {
      this.taskProcessDetailVisible = false;
    },
    handleCheckDetail() {
      this.flowChartDialogVisible = true;
    },
    finishTask1() {
      this.$refs.complatedForm1.validate(valid => {
        if (valid) {
          let flag = false;
          this.complatedForm1.issuesItems.forEach(item => {
            if (
              !item.issuesRemark ||
              !item.rectificationRemark ||
              !item.images.length
            ) {
              flag = true;
            }
          });
          if (this.complatedForm1.isIssues && flag) {
            this.$message.warning("请将需整改问题的信息填写完整");
            return;
          }
          this.finishTaskLoading = true;
          const patrolIssuesManifest = {
            issuesItems: this.complatedForm1.issuesItems,
            isRectification: this.complatedForm1.isRectification,
            patrolIssuesManifestTypeId: this.complatedForm1.issueType,
            rectificationRemark: this.complatedForm1.rectificationRemark,
            rectificationImages: this.complatedForm1.rectificationImages,
            rectificationTimeLimit: this.complatedForm1.rectificationTimeLimit
          };
          completeNodeTask(
            this.currNodeData.key,
            this.userId,
            this.complatedForm1.completeRemark,
            this.complatedForm1.taskAnnexList,
            this.complatedForm1.isIssues,
            patrolIssuesManifest
          )
            .then(res => {
              this.$message({
                type: res.data.code !== 200 ? "error" : "success",
                message: res.data.msg
              });
              this.complateDialogVisibled1 = false;
              this.getLawEventDetail();
            })
            .finally(() => {
              this.finishTaskLoading = false;
              // this.returnBack()
            });
        }
      });
    },
    finishTask() {
      const map = new Map();
      const swapList = this.complatedForm.fileList.splice(0);
      const sendFileList = swapList.filter(
        item => !map.has(item.name) && map.set(item.name, true)
      );
      // const params = {
      //   id: this.eventDetail.id,
      //   accountId: this.accountId,
      //   remark: this.complatedForm.content
      // }
      // if (sendFileList.length > 0) {
      //   params.annexList = sendFileList
      // }
      this.$refs.complatedForm.validate(valid => {
        if (valid) {
          this.finishTaskLoading = true;
          const promise = this.isNodeCompleted
            ? completeNodeTask(
                this.currNodeData.key,
                this.userId,
                this.complatedForm.content,
                sendFileList.map(item => {
                  item.base64 = item.data;
                  delete item.data;
                  return item;
                })
              )
            : completeTask(
                this.taskId,
                this.userId,
                this.complatedForm.content,
                sendFileList.map(item => {
                  item.base64 = item.data;
                  delete item.data;
                  return item;
                })
              );
          promise
            .then(res => {
              this.$message({
                type: res.data.code !== 200 ? "error" : "success",
                message: res.data.msg
              });
              this.complateDialogVisibled = false;
              this.getLawEventDetail();
            })
            .finally(() => {
              this.finishTaskLoading = false;
              // this.returnBack()
            });
        }
      });
    },
    openRestImg(index, item) {
      if (item) {
        this.resultPicList = item.annexUrl;
      }
      this.resultImageState = true;
      this.resturlIndex = index;
      document.documentElement.style.overflowY = "hidden";
    },
    closeRestImage() {
      this.resultImageState = false;
      document.documentElement.style.overflowY = "scroll";
    },
    handleType(id) {
      const curr = this.alarmTypes.find(item => item.value === id);
      return curr ? curr.label : "";
    },
    // 获取执行者列表
    getPersonList() {
      getTopTaskExecutorList().then(res => {
        const { data } = res.data;
        const data1 = {
          departmentList: data,
          userList: []
        };
        this.displayDepartmentList = this.getDepartListAll(
          JSON.parse(JSON.stringify(data1))
        ).departmentAccountList;
      });
      //  if (this.departmentType == 1) {
      //   getTopTaskExecutorList().then(res => {
      //     const { data } = res.data
      //     const data1 = {
      //       departmentList: data,
      //       userList: []
      //     }
      //     this.displayDepartmentList = this.getDepartListAll(JSON.parse(JSON.stringify(data1))).departmentAccountList
      //   })
      // } else {
      //   getPersonList(this.departmentId, this.withStreet).then((res) => {
      //     this.displayDepartmentList = this.getDepartListAll(JSON.parse(JSON
      //       .stringify(res.data.data))).departmentAccountList
      //   })
      // }
    },
    // 获取递归后的部门数组
    getDepartListAll(data) {
      const departmentAccountList = data.departmentList || [];
      const userAccountList =
        data.userList === null
          ? []
          : data.userList.map(item => {
              if (item.isAdmin) {
                item.userName = item.userName + "(管理员)";
              }
              return item;
            });
      for (const item of departmentAccountList) {
        item.needsData = { departmentId: item.departmentId };
      }
      for (const item of userAccountList) {
        item.departmentName = item.userName;
        item.needsData = {
          userId: item.userId,
          departmentId: item.departmentId,
          userName: item.userName
        };
        item.disabled = item.userId === this.userId;
      }
      data.departmentAccountList = departmentAccountList.concat(
        userAccountList
      );
      for (let i = 0; i < departmentAccountList.length; i++) {
        this.getDepartListAll(departmentAccountList[i]);
      }
      return data;
    },
    // 参数
    handleParams(args) {
      this.currNodeData = args.data;
    },
    // 新增子节点
    handleAddNode(args) {
      this.number = args;
      this.dialogDrawer = true;
      if (this.$refs.addNode) {
        this.$refs.addNode.resetFields();
      }
      this.addNode = {
        taskNodeContent: undefined,
        obj: {}
      };
      this.isAdd = true;
    },
    handleClose() {
      this.$refs.addNode.resetFields();
      this.addNode = {
        taskNodeContent: undefined,
        obj: {}
      };
      this.dialogDrawer = false;
    },
    handleaddNode() {
      this.$refs.addNode.validate(valid => {
        if (valid) {
          const newTime = new Date();
          if (newTime.getTime() - this.record1 > 400) {
            const data = JSON.parse(JSON.stringify(this.addNode));
            data.departmentId = data.obj.departmentId;
            data.userId = data.obj.userId;
            data.taskNodeType = 2;
            data.taskNodeName = data.obj.userName;
            data.permission = this.number === 1 ? 2 : 1;
            data.completionCondition = 2;
            data.operatorId = this.userId;
            data.parentId = this.currNodeData.key;
            data.taskAnnexList = data.taskAnnexList
              ? [...data.taskAnnexList]
              : [];
            delete data.obj;
            this.taskAssignLoading = true;
            assignNodeTask(data)
              .then(res => {
                if (res.data.code === 200) {
                  this.$message.success("任务指派成功");
                } else {
                  this.$message.warning(res.data.msg);
                }
                this.currNodeData = {};
                this.handleClose();
                this.getFirst();
                this.$set(this.addNode, "taskAnnexList", []);
              })
              .finally(() => {
                this.taskAssignLoading = false;
              });
          }
          this.record1 = new Date().getTime();
          // this.$refs.flowCharts1.addNode(data)
          // this.handleClose()
        }
      });
    },
    // 关闭节点
    handleNodeClose(args) {
      const { data } = args;
      this.taskNodeId = data.key;
      this.taskResultCloseVisible = true;
      this.isNodeClosed = data.parentId != null;
    },
    cancleCloseComplete(formName) {
      this.$refs[formName].resetFields();
      this.taskResultCloseVisible = false;
    },
    // 关闭任务
    completeCloseTask(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.taskResultCloseLoading = true;
          const newTime = new Date();
          if (newTime.getTime() - this.record > 400) {
            const promise = this.isNodeClosed
              ? closeNodeTask(
                  this.taskNodeId,
                  this.userId,
                  this.taskResultClose.completeRemark,
                  this.taskResultClose.taskAnnexList
                )
              : closeTask(
                  this.taskId,
                  this.userId,
                  this.taskResultClose.completeRemark
                );
            promise
              .then(res => {
                this.$message({
                  type: "success",
                  message: this.isNodeClosed ? "任务退单成功" : "任务关闭成功"
                });
                this.taskResultCloseVisible = false;
                this.taskResultCloseLoading = false;
                this.isNodeClosed = false;
                this.taskNodeId = null;
                this.executiveFeedbackList.push({
                  time: new Date(),
                  accountName: this.userName,
                  remark: this.taskResultClose.completeRemark
                });
                this.getFirst();
                this.$set(this.taskResultClose, "taskAnnexList", []);
                // this.returnToTable()
              })
              .catch(err => {
                throw new Error(err);
              })
              .finally(() => {
                // this.taskResultCloseVisible = false
                this.taskResultCloseLoading = false;
              });
          }
          this.record = new Date().getTime();
        } else {
          return false;
        }
      });
    },
    // 获取新增指派图片列表
    getAssignedBase64ImgList(imagesList) {
      this.$set(this.addNode, "taskAnnexList", imagesList);
    },
    // 获取任务退单图片列表
    getCloseBase64ImgList(imagesList) {
      this.$set(this.taskResultClose, "taskAnnexList", imagesList);
    },
    // 任务上报
    handleClickTaskPush() {
      // this.$confirm('是否确认将本任务上报?', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'success'
      //   }).then(() => {
      //     this.taskDetailLoading = true
      //     taskPush({taskId:this.taskDetail.taskId}).then(res=>{
      //       this.$message.success('上报成功')
      //       this.Refresh()
      //     }).finally(() => {
      //       this.taskDetailLoading = false
      //     })
      //   }).catch(() => {
      //     this.$message({
      //       type: 'info',
      //       message: '已取消任务上报'
      //     });
      //   });
      const h = this.$createElement;
      this.$msgbox({
        title: "提示",
        message: h("p", null, [h("span", null, "是否确认将本任务上报? ")]),
        type: "success",
        showClose: false,
        showCancelButton: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "上报中...";
            instance.showCancelButton = false;
            taskPush({ taskId: this.taskDetail.taskId })
              .then(res => {
                this.$message.success("上报成功");
                this.Refresh();
              })
              .finally(() => {
                instance.confirmButtonLoading = false;
                done();
              });
          } else {
            done();
          }
        }
      })
        .then(action => {
          console.log(action);
        })
        .catch(err => {
          this.$message.info("已取消任务上报");
        });
    },
    detail(row) {
      this.formData = { ...row };
      this.dialogVisible = true;
    },
    openImg(index, item) {
      this.urlImageList = item.map(el => el.url);
      this.imageState = true;
      this.urlIndex = index;
      document.documentElement.style.overflowY = "hidden";
    },
    closeImage() {
      this.urlImageList = [];
      this.urlIndex = 0;
      this.imageState = false;
      document.documentElement.style.overflowY = "scroll";
    }
  }
};
</script>
