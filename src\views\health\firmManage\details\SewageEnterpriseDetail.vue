<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>排污单位基本情况</h3>
    </div>
    <div class="detail-content">
      <el-card shadow="never">
        <div class="info-table">
          <div class="info-row">
            <div class="info-item">
              <span class="label">是否高风险：</span>
              <span class="value">否</span>
            </div>
            <div class="info-item">
              <span class="label">排污许可证管理类别：</span>
              <span class="value">{{ data.permitManageType || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">单位名称：</span>
              <span class="value">{{ data.enterName || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">注册地址：</span>
              <span class="value">{{ data.regAddress || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">生产经营场所地址：</span>
              <span class="value">{{ data.opeAddress || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">邮政编码：</span>
              <span class="value">{{ data.postCode || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label">行业类别：</span>
              <span class="value">{{ data.industryName || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">所属行业类别：</span>
              <span class="value">{{ data.industryParentName || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">直接负责：</span>
              <span class="value">否</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">投产日期：</span>
              <span class="value">{{ formatDate(data.operaTime) || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">生产经营场所中心经度：</span>
              <span class="value">{{ data.lng || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">生产经营场所中心纬度：</span>
              <span class="value">{{ data.lat || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">组织机构代码：</span>
              <span class="value">{{ data.organCode || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">统一社会信用代码：</span>
              <span class="value">{{ data.creditCode || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">法定代表人（主要负责人）：</span>
              <span class="value highlight">{{ data.legalPerson || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">技术负责人：</span>
              <span class="value">{{ data.concatPerson || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">固定电话：</span>
              <span class="value">{{ data.officePhone || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">移动电话：</span>
              <span class="value">{{ data.mobilePhone || "-" }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">所在地是否属于大气重点控制区域：</span>
              <span class="value">{{ formatYesNo(data.isSpecial) }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">所在地是否属于总磷控制区域：</span>
              <span class="value">{{ formatYesNo(data.isPhosphorus) }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">所在地是否属于总氮控制区域：</span>
              <span class="value">{{ formatYesNo(data.isTotalNitrogen) }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item full-width">
              <span class="label"
                >所在地是否属于重金属污染物特别排放限值实施区域：</span
              >
              <span class="value">{{
                formatYesNo(data.isHeavyMetalArea)
              }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">是否位于工业园区：</span>
              <span class="value">{{ formatYesNo(data.isPark) }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">所属工业园区名称：</span>
              <span class="value">{{ data.industrialParkName || "-" }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: "SewageEnterpriseDetail",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 组件内部数据
    };
  },
  created() {
    console.log(this.data, "======================");
  },
  methods: {
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "-";
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;
      return date
        .toLocaleDateString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit"
        })
        .replace(/\//g, "-");
    },

    // 格式化是否字段 (0-否,1-是)
    formatYesNo(value) {
      if (value === "1" || value === 1) return "是";
      if (value === "0" || value === 0) return "否";
      return "-";
    }
  }
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow-y: auto;

    .info-table {
      .info-row {
        display: flex;
        margin-bottom: 16px;

        .info-item {
          display: flex;
          flex: 1;
          align-items: flex-start;
          margin-right: 40px;

          &:last-child {
            margin-right: 0;
          }

          &.full-width {
            flex: 1;
            margin-right: 0;
          }

          .label {
            flex-shrink: 0;
            min-width: 180px;
            margin-right: 8px;
            font-size: 14px;
            line-height: 22px;
            color: #666666;
            text-align: right;
          }

          .value {
            font-size: 14px;
            line-height: 22px;
            color: #333333;
            word-break: break-all;

            &.highlight {
              color: #1890ff;
            }
          }
        }

        // 单个字段占满整行的情况
        &:has(.info-item:only-child) {
          .info-item {
            margin-right: 0;
          }
        }
      }
    }
  }
}
</style>
