<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>排污单位基本情况</h3>
    </div>
    <div class="detail-content">
      <el-card shadow="never">
        <div class="content-placeholder">
          <p>排污单位基本情况详情内容</p>
          <p>待实现具体内容...</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SewageEnterpriseDetail',
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 组件内部数据
    }
  },
  created() {
    console.log(this.data, '======================');
  },
  methods: {
    // 组件方法
  }
}
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .detail-content {
    padding: 16px;
    height: calc(100% - 60px);
    overflow-y: auto;

    .content-placeholder {
      text-align: center;
      color: #999;
      padding: 40px;

      p {
        margin: 16px 0;
        font-size: 14px;
      }
    }
  }
}
</style>
